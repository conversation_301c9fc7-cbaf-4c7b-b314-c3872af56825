// Reports functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set report date
    const now = new Date();
    const formattedDate = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
    document.getElementById('report-date').textContent = `تاريخ التقرير: ${formattedDate}`;

    // Load classes for filter
    loadClasses();

    // Check for student_id in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const studentId = urlParams.get('student_id');

    // Handle print button
    document.getElementById('printReport').addEventListener('click', function() {
        window.print();
    });

    // Handle form submission
    document.getElementById('report-filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        generateReport();
    });

    // Handle export buttons
    document.getElementById('export-excel-btn').addEventListener('click', function() {
        exportToExcel();
    });

    document.getElementById('export-pdf-btn').addEventListener('click', function() {
        exportToPDF();
    });

    // Generate initial report
    generateReport(studentId);

    // Load charts when charts tab is shown
    document.getElementById('charts-tab').addEventListener('click', function() {
        setTimeout(generateCharts, 100); // Small delay to ensure canvas is visible
    });
});

// Load classes for filter
function loadClasses() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const classSelect = document.getElementById('class');

    // Get unique classes
    const classes = [...new Set(students.map(student => student.class))].sort();

    classes.forEach(className => {
        const option = document.createElement('option');
        option.value = className;
        option.textContent = className;
        classSelect.appendChild(option);
    });
}

// Generate report
function generateReport(specificStudentId = null) {
    // Get filter values
    const selectedClass = document.getElementById('class').value;
    const reportType = document.getElementById('report_type').value;
    const debtMin = parseFloat(document.getElementById('debt_min').value) || 0;

    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Calculate student data
    const studentData = [];

    students.forEach(student => {
        // Skip if not the specific student (if provided)
        if (specificStudentId && student.id != specificStudentId) {
            return;
        }

        // Skip if class filter is applied and doesn't match
        if (selectedClass && student.class !== selectedClass) {
            return;
        }

        let totalPurchases = 0;
        let totalPayments = 0;

        // Calculate purchases and payments
        transactions.forEach(transaction => {
            if (transaction.student_id === student.id) {
                if (transaction.type === 'purchase') {
                    totalPurchases += parseFloat(transaction.amount);
                } else if (transaction.type === 'payment') {
                    totalPayments += parseFloat(transaction.amount);
                }
            }
        });

        const balance = totalPurchases - totalPayments;

        // Skip based on report type
        if ((reportType === 'debt_only' && balance <= 0) ||
            (reportType === 'credit_only' && balance >= 0)) {
            return;
        }

        // Skip if debt is less than minimum (for debt_only reports)
        if (reportType === 'debt_only' && balance < debtMin) {
            return;
        }

        // Add student to data array
        studentData.push({
            id: student.id,
            name: student.name,
            class: student.class,
            parent_name: student.parent_name,
            phone_number: student.phone_number,
            total_purchases: totalPurchases,
            total_payments: totalPayments,
            balance: balance
        });
    });

    // Sort by class and name
    studentData.sort((a, b) => {
        if (a.class === b.class) {
            return a.name.localeCompare(b.name);
        }
        return a.class.localeCompare(b.class);
    });

    // Update class title if filter is applied
    const classTitle = document.getElementById('class-title');
    if (selectedClass) {
        classTitle.textContent = `الصف: ${selectedClass}`;
        classTitle.classList.remove('d-none');
    } else {
        classTitle.classList.add('d-none');
    }

    // Display report
    const tableBody = document.getElementById('report-table-body');
    tableBody.innerHTML = '';

    let totalDebt = 0;
    let totalCredit = 0;

    if (studentData.length > 0) {
        studentData.forEach((student, index) => {
            // Update totals
            if (student.balance > 0) {
                totalDebt += student.balance;
            } else {
                totalCredit += Math.abs(student.balance);
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${student.name}</td>
                <td>${student.class}</td>
                <td>${student.parent_name}</td>
                <td>${student.phone_number}</td>
                <td class="text-end">${formatKuwaitiCurrency(student.total_purchases)}</td>
                <td class="text-end">${formatKuwaitiCurrency(student.total_payments)}</td>
                <td class="text-end">
                    ${student.balance > 0
                        ? `<span class="text-danger">${formatKuwaitiCurrency(student.balance)}</span>`
                        : student.balance < 0
                            ? `<span class="text-success">${formatKuwaitiCurrency(Math.abs(student.balance))} (رصيد)</span>`
                            : `<span class="text-muted">0 فلس</span>`}
                </td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="8" class="text-center">
                لا توجد بيانات تطابق معايير البحث.
            </td>
        `;
        tableBody.appendChild(row);
    }

    // Update totals
    document.getElementById('total-debt').textContent = formatKuwaitiCurrency(totalDebt);
    document.getElementById('total-credit').textContent = formatKuwaitiCurrency(totalCredit);

    const netBalance = totalDebt - totalCredit;
    const netBalanceElement = document.getElementById('net-balance');

    if (netBalance > 0) {
        netBalanceElement.textContent = `${formatKuwaitiCurrency(netBalance)} (دين)`;
        netBalanceElement.className = 'text-end text-danger';
    } else if (netBalance < 0) {
        netBalanceElement.textContent = `${formatKuwaitiCurrency(Math.abs(netBalance))} (رصيد)`;
        netBalanceElement.className = 'text-end text-success';
    } else {
        netBalanceElement.textContent = '0 فلس';
        netBalanceElement.className = 'text-end';
    }

    // Store report data for export
    window.reportData = {
        studentData: studentData,
        totalDebt: totalDebt,
        totalCredit: totalCredit,
        netBalance: netBalance,
        filters: {
            class: selectedClass,
            reportType: reportType,
            debtMin: debtMin
        }
    };
}

// Generate charts
function generateCharts() {
    // Load Chart.js dynamically if not already loaded
    if (typeof Chart === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
        script.onload = function() {
            createCharts();
        };
        document.head.appendChild(script);
    } else {
        createCharts();
    }
}

// Create charts
function createCharts() {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Calculate student debts
    const studentDebts = {};
    const classDebts = {};
    const monthlyTransactions = {
        purchases: {},
        payments: {}
    };

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            id: student.id,
            name: student.name,
            class: student.class,
            total_purchases: 0,
            total_payments: 0,
            balance: 0
        };

        // Initialize class debts
        if (!classDebts[student.class]) {
            classDebts[student.class] = {
                total_debt: 0,
                count: 0
            };
        }
        classDebts[student.class].count++;
    });

    // Calculate debts and monthly transactions
    transactions.forEach(transaction => {
        const date = new Date(transaction.date);
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].total_purchases += parseFloat(transaction.amount);

            // Add to monthly purchases
            if (!monthlyTransactions.purchases[monthYear]) {
                monthlyTransactions.purchases[monthYear] = 0;
            }
            monthlyTransactions.purchases[monthYear] += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].total_payments += parseFloat(transaction.amount);

            // Add to monthly payments
            if (!monthlyTransactions.payments[monthYear]) {
                monthlyTransactions.payments[monthYear] = 0;
            }
            monthlyTransactions.payments[monthYear] += parseFloat(transaction.amount);
        }
    });

    // Calculate balances and class debts
    Object.values(studentDebts).forEach(student => {
        student.balance = student.total_purchases - student.total_payments;
        if (student.balance > 0) {
            classDebts[student.class].total_debt += student.balance;
        }
    });

    // Create Debt by Class Chart
    createDebtByClassChart(classDebts);

    // Create Transactions by Month Chart
    createTransactionsByMonthChart(monthlyTransactions);

    // Create Top Debtors Chart
    createTopDebtorsChart(studentDebts);

    // Create Debt vs Payment Chart
    createDebtVsPaymentChart(studentDebts);
}

// Create Debt by Class Chart
function createDebtByClassChart(classDebts) {
    const canvas = document.getElementById('debtByClassChart');

    // Clear existing chart if any
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Prepare data
    const labels = Object.keys(classDebts);
    const data = labels.map(className => classDebts[className].total_debt);
    const backgroundColor = generateColors(labels.length);

    // Create chart
    canvas.chart = new Chart(canvas, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                label: 'إجمالي الديون',
                data: data,
                backgroundColor: backgroundColor,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${currencyFormat.format(value)}`;
                        }
                    }
                }
            }
        }
    });
}

// Create Transactions by Month Chart
function createTransactionsByMonthChart(monthlyTransactions) {
    const canvas = document.getElementById('transactionsByMonthChart');

    // Clear existing chart if any
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Get all months
    const allMonths = new Set([
        ...Object.keys(monthlyTransactions.purchases),
        ...Object.keys(monthlyTransactions.payments)
    ]);

    // Sort months
    const sortedMonths = Array.from(allMonths).sort((a, b) => {
        const [aMonth, aYear] = a.split('/').map(Number);
        const [bMonth, bYear] = b.split('/').map(Number);

        if (aYear !== bYear) {
            return aYear - bYear;
        }
        return aMonth - bMonth;
    });

    // Prepare data
    const purchasesData = sortedMonths.map(month => monthlyTransactions.purchases[month] || 0);
    const paymentsData = sortedMonths.map(month => monthlyTransactions.payments[month] || 0);

    // Create chart
    canvas.chart = new Chart(canvas, {
        type: 'bar',
        data: {
            labels: sortedMonths,
            datasets: [
                {
                    label: 'المشتريات',
                    data: purchasesData,
                    backgroundColor: 'rgba(220, 53, 69, 0.7)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1
                },
                {
                    label: 'المدفوعات',
                    data: paymentsData,
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'الشهر/السنة'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبلغ (د.ك)'
                    },
                    ticks: {
                        callback: function(value) {
                            return currencyFormat.format(value);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${currencyFormat.format(value)}`;
                        }
                    }
                }
            }
        }
    });
}

// Create Top Debtors Chart
function createTopDebtorsChart(studentDebts) {
    const canvas = document.getElementById('topDebtorsChart');

    // Clear existing chart if any
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Get top 10 debtors
    const topDebtors = Object.values(studentDebts)
        .filter(student => student.balance > 0)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 10);

    // Prepare data
    const labels = topDebtors.map(student => student.name);
    const data = topDebtors.map(student => student.balance);

    // Create chart
    canvas.chart = new Chart(canvas, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'الدين',
                data: data,
                backgroundColor: 'rgba(220, 53, 69, 0.7)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبلغ (د.ك)'
                    },
                    ticks: {
                        callback: function(value) {
                            return currencyFormat.format(value);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${currencyFormat.format(value)}`;
                        }
                    }
                }
            }
        }
    });
}

// Create Debt vs Payment Chart
function createDebtVsPaymentChart(studentDebts) {
    const canvas = document.getElementById('debtVsPaymentChart');

    // Clear existing chart if any
    if (canvas.chart) {
        canvas.chart.destroy();
    }

    // Calculate total purchases and payments by class
    const classTotals = {};

    Object.values(studentDebts).forEach(student => {
        if (!classTotals[student.class]) {
            classTotals[student.class] = {
                purchases: 0,
                payments: 0
            };
        }

        classTotals[student.class].purchases += student.total_purchases;
        classTotals[student.class].payments += student.total_payments;
    });

    // Prepare data
    const labels = Object.keys(classTotals).sort();
    const purchasesData = labels.map(className => classTotals[className].purchases);
    const paymentsData = labels.map(className => classTotals[className].payments);

    // Create chart
    canvas.chart = new Chart(canvas, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'المشتريات',
                    data: purchasesData,
                    backgroundColor: 'rgba(220, 53, 69, 0.2)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'المدفوعات',
                    data: paymentsData,
                    backgroundColor: 'rgba(40, 167, 69, 0.2)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'المبلغ (د.ك)'
                    },
                    ticks: {
                        callback: function(value) {
                            return currencyFormat.format(value);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw || 0;
                            return `${label}: ${currencyFormat.format(value)}`;
                        }
                    }
                }
            }
        }
    });
}

// Export to Excel
function exportToExcel() {
    // Load SheetJS (xlsx) dynamically if not already loaded
    if (typeof XLSX === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
        script.onload = function() {
            performExcelExport();
        };
        document.head.appendChild(script);
    } else {
        performExcelExport();
    }
}

// Perform Excel export
function performExcelExport() {
    if (!window.reportData || !window.reportData.studentData) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // Prepare data for export
    const exportData = window.reportData.studentData.map(student => {
        return {
            'اسم الطالب': student.name,
            'الصف': student.class,
            'ولي الأمر': student.parent_name,
            'رقم الجوال': student.phone_number,
            'إجمالي المشتريات': formatKuwaitiCurrency(student.total_purchases),
            'إجمالي المدفوعات': formatKuwaitiCurrency(student.total_payments),
            'الرصيد': student.balance > 0
                ? formatKuwaitiCurrency(student.balance) + ' (دين)'
                : student.balance < 0
                    ? formatKuwaitiCurrency(Math.abs(student.balance)) + ' (رصيد)'
                    : '0 فلس'
        };
    });

    // Add summary row
    exportData.push({
        'اسم الطالب': 'الإجمالي',
        'الصف': '',
        'ولي الأمر': '',
        'رقم الجوال': '',
        'إجمالي المشتريات': '',
        'إجمالي المدفوعات': '',
        'الرصيد': window.reportData.netBalance > 0
            ? formatKuwaitiCurrency(window.reportData.netBalance) + ' (دين)'
            : window.reportData.netBalance < 0
                ? formatKuwaitiCurrency(Math.abs(window.reportData.netBalance)) + ' (رصيد)'
                : '0 فلس'
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير الديون');

    // Generate Excel file
    XLSX.writeFile(workbook, 'تقرير_ديون_المقصف.xlsx');
}

// Export to PDF
function exportToPDF() {
    // Load jsPDF dynamically if not already loaded
    if (typeof jsPDF === 'undefined') {
        const script1 = document.createElement('script');
        script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';

        const script2 = document.createElement('script');
        script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js';

        script1.onload = function() {
            document.head.appendChild(script2);
        };

        script2.onload = function() {
            performPDFExport();
        };

        document.head.appendChild(script1);
    } else {
        performPDFExport();
    }
}

// Perform PDF export
function performPDFExport() {
    if (!window.reportData || !window.reportData.studentData) {
        alert('لا توجد بيانات للتصدير');
        return;
    }

    // Create PDF
    const { jsPDF } = window.jspdf;

    // Load Arabic font
    const loadArabicFont = () => {
        // Add Amiri font for Arabic support
        const amiriNormal = 'https://cdn.jsdelivr.net/npm/amiri-font@0.7.0/amiri-regular.ttf';
        const amiriBold = 'https://cdn.jsdelivr.net/npm/amiri-font@0.7.0/amiri-bold.ttf';

        // Create a new XMLHttpRequest for the font
        const xhr = new XMLHttpRequest();
        xhr.open('GET', amiriNormal, true);
        xhr.responseType = 'arraybuffer';

        xhr.onload = function() {
            if (xhr.status === 200) {
                // Add the font to the PDF
                const fontData = xhr.response;
                const doc = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });

                // Add font
                doc.addFileToVFS('Amiri-Regular.ttf', arrayBufferToBase64(fontData));
                doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');

                // Set font
                doc.setFont('Amiri');

                // Set right-to-left mode
                doc.setR2L(true);

                // Continue with PDF generation
                generatePDFContent(doc);
            } else {
                // Fallback if font loading fails
                const doc = new jsPDF({
                    orientation: 'landscape',
                    unit: 'mm',
                    format: 'a4'
                });
                doc.setR2L(true);
                generatePDFContent(doc);
            }
        };

        xhr.onerror = function() {
            // Fallback if font loading fails
            const doc = new jsPDF({
                orientation: 'landscape',
                unit: 'mm',
                format: 'a4'
            });
            doc.setR2L(true);
            generatePDFContent(doc);
        };

        xhr.send();
    };

    // Convert ArrayBuffer to Base64
    function arrayBufferToBase64(buffer) {
        let binary = '';
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    // Start loading font
    loadArabicFont();
}

// Generate PDF content
function generatePDFContent(doc) {
    // Add title
    doc.setFontSize(18);
    doc.text('تقرير ديون المقصف المدرسي', doc.internal.pageSize.width / 2, 15, { align: 'center' });

    // Add date
    doc.setFontSize(12);
    doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-KW')}`, doc.internal.pageSize.width / 2, 22, { align: 'center' });

    // Add class filter if applied
    if (window.reportData.filters.class) {
        doc.text(`الصف: ${window.reportData.filters.class}`, doc.internal.pageSize.width / 2, 29, { align: 'center' });
    }

    // Prepare table data
    const tableData = window.reportData.studentData.map(student => [
        student.name,
        student.class,
        student.parent_name,
        student.phone_number,
        formatKuwaitiCurrency(student.total_purchases),
        formatKuwaitiCurrency(student.total_payments),
        student.balance > 0
            ? formatKuwaitiCurrency(student.balance)
            : student.balance < 0
                ? formatKuwaitiCurrency(Math.abs(student.balance)) + ' (رصيد)'
                : '0 فلس'
    ]);

    // Add summary row
    tableData.push([
        'الإجمالي',
        '',
        '',
        '',
        '',
        '',
        window.reportData.netBalance > 0
            ? formatKuwaitiCurrency(window.reportData.netBalance) + ' (دين)'
            : window.reportData.netBalance < 0
                ? formatKuwaitiCurrency(Math.abs(window.reportData.netBalance)) + ' (رصيد)'
                : '0 فلس'
    ]);

    // Add table
    doc.autoTable({
        head: [['اسم الطالب', 'الصف', 'ولي الأمر', 'رقم الجوال', 'إجمالي المشتريات', 'إجمالي المدفوعات', 'الرصيد']],
        body: tableData,
        startY: window.reportData.filters.class ? 35 : 29,
        theme: 'grid',
        styles: {
            font: doc.getFont().fontName, // Use the loaded Arabic font
            halign: 'right',
            textColor: [0, 0, 0],
            lineColor: [0, 0, 0],
            lineWidth: 0.1
        },
        headStyles: {
            fillColor: [41, 128, 185],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [240, 240, 240]
        },
        footStyles: {
            fillColor: [220, 220, 220],
            fontStyle: 'bold'
        }
    });

    // Add footer
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.text('تم إنشاء هذا التقرير بواسطة نظام إدارة ديون المقصف المدرسي', doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });
        doc.text(`الصفحة ${i} من ${pageCount}`, doc.internal.pageSize.width - 20, doc.internal.pageSize.height - 10);
    }

    // Save PDF
    doc.save('تقرير_ديون_المقصف.pdf');
}

// Generate random colors for charts
function generateColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        const hue = (i * 137) % 360; // Use golden angle approximation for better distribution
        colors.push(`hsl(${hue}, 70%, 60%)`);
    }
    return colors;
}