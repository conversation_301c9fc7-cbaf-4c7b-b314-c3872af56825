// Sample data for testing

// Function to initialize sample data if not exists
function initializeSampleData() {
    // Initialize documents if not exists
    if (!localStorage.getItem('documents') || JSON.parse(localStorage.getItem('documents')).length === 0) {
        // Sample documents (invoices and receipts)
        const documents = [
            // إضافة المزيد من الفواتير والسندات للاختبار
            {
                id: 1,
                type: 'invoice',
                document_number: 'INV-2025-0008',
                student_id: 1,
                date: '2025-04-23',
                amount: 1.200,
                description: 'وجبة غداء وعصير',
                created_by: 1,
                created_at: '2025-04-23T10:30:00'
            },
            {
                id: 2,
                type: 'invoice',
                document_number: 'INV-2025-0003',
                student_id: 2,
                date: '2025-04-22',
                amount: 0.035,
                description: 'سناك',
                created_by: 1,
                created_at: '2025-04-22T09:45:00'
            },
            {
                id: 3,
                type: 'invoice',
                document_number: 'INV-2025-0004',
                student_id: 2,
                date: '2025-04-22',
                amount: 0.035,
                description: 'سناك',
                created_by: 1,
                created_at: '2025-04-22T11:15:00'
            },
            {
                id: 4,
                type: 'receipt',
                document_number: 'REC-2025-0001',
                student_id: 1,
                date: '2025-04-24',
                amount: 1.200,
                description: 'سداد فاتورة رقم INV-2025-0008',
                payment_method: 'cash',
                reference_number: '',
                created_by: 1,
                created_at: '2025-04-24T14:20:00'
            },
            {
                id: 5,
                type: 'receipt',
                document_number: 'REC-2025-0002',
                student_id: 2,
                date: '2025-04-23',
                amount: 0.070,
                description: 'سداد فواتير متعددة',
                payment_method: 'bank_transfer',
                reference_number: 'TR12345',
                created_by: 1,
                created_at: '2025-04-23T09:30:00'
            },
            // فواتير وسندات إضافية
            {
                id: 6,
                type: 'invoice',
                document_number: 'INV-2025-0005',
                student_id: 3,
                date: '2025-04-21',
                amount: 0.750,
                description: 'وجبة خفيفة',
                created_by: 1,
                created_at: '2025-04-21T08:30:00'
            },
            {
                id: 7,
                type: 'invoice',
                document_number: 'INV-2025-0006',
                student_id: 4,
                date: '2025-04-20',
                amount: 1.500,
                description: 'وجبة غداء كاملة',
                created_by: 1,
                created_at: '2025-04-20T12:15:00'
            },
            {
                id: 8,
                type: 'receipt',
                document_number: 'REC-2025-0003',
                student_id: 3,
                date: '2025-04-22',
                amount: 0.750,
                description: 'سداد فاتورة رقم INV-2025-0005',
                payment_method: 'cash',
                reference_number: '',
                created_by: 1,
                created_at: '2025-04-22T09:45:00'
            },
            {
                id: 9,
                type: 'receipt',
                document_number: 'REC-2025-0004',
                student_id: 4,
                date: '2025-04-21',
                amount: 1.000,
                description: 'دفعة جزئية لفاتورة رقم INV-2025-0006',
                payment_method: 'bank_transfer',
                reference_number: 'TR67890',
                created_by: 1,
                created_at: '2025-04-21T14:30:00'
            }
        ];

        // Save to localStorage
        localStorage.setItem('documents', JSON.stringify(documents));
        console.log('Sample documents data initialized');
    }
    // Check if students data exists
    if (!localStorage.getItem('students') || JSON.parse(localStorage.getItem('students')).length === 0) {
        // Sample students
        const students = [
            {
                id: 1,
                name: "أحمد محمد",
                student_id: "ST001",
                class: "الصف العاشر",
                parent_name: "محمد أحمد",
                phone_number: "***********",
                email: "<EMAIL>",
                address: "الكويت، حولي"
            },
            {
                id: 2,
                name: "فاطمة علي",
                student_id: "ST002",
                class: "الصف التاسع",
                parent_name: "علي حسن",
                phone_number: "96587654321",
                email: "<EMAIL>",
                address: "الكويت، السالمية"
            },
            {
                id: 3,
                name: "محمد خالد",
                student_id: "ST003",
                class: "الصف العاشر",
                parent_name: "خالد محمد",
                phone_number: "96599887766",
                email: "<EMAIL>",
                address: "الكويت، الجهراء"
            },
            {
                id: 4,
                name: "نورة سالم",
                student_id: "ST004",
                class: "الصف الحادي عشر",
                parent_name: "سالم عبدالله",
                phone_number: "96566778899",
                email: "<EMAIL>",
                address: "الكويت، الفروانية"
            },
            {
                id: 5,
                name: "عبدالله ناصر",
                student_id: "ST005",
                class: "الصف الحادي عشر",
                parent_name: "ناصر عبدالله",
                phone_number: "96555443322",
                email: "<EMAIL>",
                address: "الكويت، مبارك الكبير"
            }
        ];

        // Save to localStorage
        localStorage.setItem('students', JSON.stringify(students));
        console.log('Sample students data initialized');
    }

    // Check if transactions data exists
    if (!localStorage.getItem('transactions') || JSON.parse(localStorage.getItem('transactions')).length === 0) {
        // Sample transactions
        const transactions = [
            {
                id: 1,
                student_id: 1,
                type: "purchase",
                amount: 2.500,
                description: "وجبة غداء",
                date: "2023-10-01",
                created_by: 1,
                created_at: "2023-10-01T10:30:00"
            },
            {
                id: 2,
                student_id: 1,
                type: "purchase",
                amount: 1.750,
                description: "سناك",
                date: "2023-10-02",
                created_by: 1,
                created_at: "2023-10-02T09:45:00"
            },
            {
                id: 3,
                student_id: 1,
                type: "payment",
                amount: 3.000,
                description: "دفعة جزئية",
                date: "2023-10-05",
                created_by: 1,
                created_at: "2023-10-05T14:20:00"
            },
            {
                id: 4,
                student_id: 2,
                type: "purchase",
                amount: 3.250,
                description: "وجبة غداء وعصير",
                date: "2023-10-01",
                created_by: 1,
                created_at: "2023-10-01T11:15:00"
            },
            {
                id: 5,
                student_id: 2,
                type: "payment",
                amount: 3.250,
                description: "دفعة كاملة",
                date: "2023-10-07",
                created_by: 1,
                created_at: "2023-10-07T08:30:00"
            },
            {
                id: 6,
                student_id: 3,
                type: "purchase",
                amount: 4.500,
                description: "وجبة غداء وسناك",
                date: "2023-10-03",
                created_by: 1,
                created_at: "2023-10-03T12:00:00"
            },
            {
                id: 7,
                student_id: 4,
                type: "purchase",
                amount: 2.000,
                description: "سناك",
                date: "2023-10-04",
                created_by: 1,
                created_at: "2023-10-04T10:10:00"
            },
            {
                id: 8,
                student_id: 5,
                type: "purchase",
                amount: 3.750,
                description: "وجبة غداء وعصير",
                date: "2023-10-05",
                created_by: 1,
                created_at: "2023-10-05T11:45:00"
            },
            {
                id: 9,
                student_id: 5,
                type: "payment",
                amount: 2.000,
                description: "دفعة جزئية",
                date: "2023-10-08",
                created_by: 1,
                created_at: "2023-10-08T09:00:00"
            }
        ];

        // Save to localStorage
        localStorage.setItem('transactions', JSON.stringify(transactions));
        console.log('Sample transactions data initialized');
    }

    // Check if installment plans data exists
    if (!localStorage.getItem('installmentPlans') || JSON.parse(localStorage.getItem('installmentPlans')).length === 0) {
        // Sample installment plans
        const installmentPlans = [
            {
                id: 1,
                student_id: 3,
                student_name: "محمد خالد",
                student_class: "الصف العاشر",
                total_amount: 4.500,
                installment_count: 3,
                frequency: "monthly",
                start_date: "2023-10-15",
                end_date: "2023-12-15",
                notes: "تقسيط دين المقصف",
                status: "active",
                created_at: "2023-10-10T09:30:00",
                created_by: 1,
                installments: [
                    {
                        index: 1,
                        amount: 1.500,
                        due_date: "2023-10-15",
                        status: "paid",
                        payment_date: "2023-10-15",
                        payment_method: "cash",
                        notes: null
                    },
                    {
                        index: 2,
                        amount: 1.500,
                        due_date: "2023-11-15",
                        status: "pending",
                        payment_date: null,
                        payment_method: null,
                        notes: null
                    },
                    {
                        index: 3,
                        amount: 1.500,
                        due_date: "2023-12-15",
                        status: "pending",
                        payment_date: null,
                        payment_method: null,
                        notes: null
                    }
                ]
            },
            {
                id: 2,
                student_id: 5,
                student_name: "عبدالله ناصر",
                student_class: "الصف الحادي عشر",
                total_amount: 1.750,
                installment_count: 2,
                frequency: "monthly",
                start_date: "2023-10-20",
                end_date: "2023-11-20",
                notes: "تقسيط دين المقصف",
                status: "active",
                created_at: "2023-10-10T10:15:00",
                created_by: 1,
                installments: [
                    {
                        index: 1,
                        amount: 0.875,
                        due_date: "2023-10-20",
                        status: "pending",
                        payment_date: null,
                        payment_method: null,
                        notes: null
                    },
                    {
                        index: 2,
                        amount: 0.875,
                        due_date: "2023-11-20",
                        status: "pending",
                        payment_date: null,
                        payment_method: null,
                        notes: null
                    }
                ]
            }
        ];

        // Save to localStorage
        localStorage.setItem('installmentPlans', JSON.stringify(installmentPlans));
        console.log('Sample installment plans data initialized');
    }

    // Initialize current user if not exists
    if (!sessionStorage.getItem('currentUser')) {
        const currentUser = {
            id: 1,
            username: "admin",
            name: "مدير النظام",
            role: "admin"
        };

        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
        console.log('Sample current user initialized');
    }
}

// Call the initialization function
initializeSampleData();
