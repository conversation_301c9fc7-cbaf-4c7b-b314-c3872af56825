<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="css/dark-mode.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.html">المعاملات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="system-settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="installments.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> نظام الأقساط
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="users.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-users-cog me-2"></i> إدارة المستخدمين
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            قم بتعديل إعدادات النظام حسب احتياجاتك. تأكد من حفظ التغييرات بعد الانتهاء.
                        </div>
                        
                        <!-- Settings Form -->
                        <form id="system-settings-form">
                            <!-- School Information Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-school me-2"></i> معلومات المدرسة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="school-name" class="form-label">اسم المدرسة</label>
                                            <input type="text" class="form-control" id="school-name" placeholder="أدخل اسم المدرسة">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="academic-year" class="form-label">العام الدراسي</label>
                                            <input type="text" class="form-control" id="academic-year" placeholder="مثال: 2023-2024">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="school-logo" class="form-label">شعار المدرسة</label>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <img id="logo-preview" src="img/default-logo.png" alt="School Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                            </div>
                                            <div class="flex-grow-1">
                                                <input type="file" class="form-control" id="school-logo" accept="image/*">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="school-address" class="form-label">عنوان المدرسة</label>
                                        <textarea class="form-control" id="school-address" rows="2" placeholder="أدخل عنوان المدرسة"></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="school-phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="school-phone" placeholder="أدخل رقم الهاتف">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="school-email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="school-email" placeholder="أدخل البريد الإلكتروني">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Currency Settings Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-money-bill me-2"></i> إعدادات العملة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="currency-name" class="form-label">اسم العملة</label>
                                            <input type="text" class="form-control" id="currency-name" value="دينار كويتي">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="currency-code" class="form-label">رمز العملة</label>
                                            <input type="text" class="form-control" id="currency-code" value="KWD">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="currency-symbol" class="form-label">الرمز المختصر</label>
                                            <input type="text" class="form-control" id="currency-symbol" value="د.ك">
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="small-currency-name" class="form-label">اسم العملة الصغرى</label>
                                            <input type="text" class="form-control" id="small-currency-name" value="فلس">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="small-currency-ratio" class="form-label">نسبة العملة الصغرى</label>
                                            <input type="number" class="form-control" id="small-currency-ratio" value="1000">
                                            <div class="form-text">مثال: 1 دينار = 1000 فلس</div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="show-small-currency" checked>
                                        <label class="form-check-label" for="show-small-currency">
                                            عرض المبالغ الصغيرة بالعملة الصغرى (مثال: 500 فلس بدلاً من 0.500 دينار)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Notification Settings Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bell me-2"></i> إعدادات الإشعارات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable-notifications" checked>
                                        <label class="form-check-label" for="enable-notifications">
                                            تفعيل نظام الإشعارات
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable-whatsapp-notifications" checked>
                                        <label class="form-check-label" for="enable-whatsapp-notifications">
                                            تفعيل الإشعارات عبر الواتساب
                                        </label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="default-country-code" class="form-label">رمز الدولة الافتراضي</label>
                                        <input type="text" class="form-control" id="default-country-code" value="+965">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="debt-notification-threshold" class="form-label">الحد الأدنى للدين لإرسال الإشعارات (بالدينار)</label>
                                        <input type="number" class="form-control" id="debt-notification-threshold" value="5.000" step="0.001">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- System Backup Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-database me-2"></i> النسخ الاحتياطي
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="enable-auto-backup" checked>
                                        <label class="form-check-label" for="enable-auto-backup">
                                            تفعيل النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="backup-frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                        <select class="form-select" id="backup-frequency">
                                            <option value="daily">يومي</option>
                                            <option value="weekly" selected>أسبوعي</option>
                                            <option value="monthly">شهري</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" id="backup-now-btn" class="btn btn-primary">
                                            <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية الآن
                                        </button>
                                        
                                        <button type="button" id="restore-backup-btn" class="btn btn-warning">
                                            <i class="fas fa-upload me-1"></i> استعادة من نسخة احتياطية
                                        </button>
                                        
                                        <input type="file" id="backup-file" class="d-none" accept=".json">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Advanced Settings Section -->
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2"></i> إعدادات متقدمة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        تحذير: هذه الإعدادات متقدمة وقد تؤدي إلى فقدان البيانات. يرجى توخي الحذر.
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" id="reset-settings-btn" class="btn btn-outline-secondary">
                                            <i class="fas fa-redo me-1"></i> إعادة تعيين الإعدادات الافتراضية
                                        </button>
                                        
                                        <button type="button" id="clear-all-data-btn" class="btn btn-outline-danger">
                                            <i class="fas fa-trash-alt me-1"></i> حذف جميع البيانات
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Save Button -->
                            <div class="d-grid">
                                <button type="submit" id="save-settings-btn" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-1"></i> حفظ الإعدادات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>
    
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">تأكيد العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmation-message">
                    هل أنت متأكد من أنك تريد المتابعة؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirm-action-btn">تأكيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/system-settings.js"></script>
    <script src="js/dark-mode.js"></script>
</body>
</html>
