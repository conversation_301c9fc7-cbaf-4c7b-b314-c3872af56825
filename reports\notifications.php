<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get student ID from URL if provided
$student_id = $_GET['student_id'] ?? null;

// Get filter parameters
$class = $_GET['class'] ?? '';
$debt_min = $_GET['debt_min'] ?? '50';
$notification_type = $_GET['notification_type'] ?? 'sms';

// Get all classes for filter
$stmt = $pdo->query('SELECT DISTINCT class FROM students ORDER BY class');
$classes = $stmt->fetchAll();

// Build query
$query = 'SELECT s.*,
    COALESCE(SUM(CASE WHEN t.type = "purchase" THEN t.amount ELSE 0 END), 0) -
    COALESCE(SUM(CASE WHEN t.type = "payment" THEN t.amount ELSE 0 END), 0) as debt
    FROM students s
    LEFT JOIN transactions t ON s.id = t.student_id
    WHERE 1=1';
$params = [];

if (!empty($student_id)) {
    $query .= ' AND s.id = ?';
    $params[] = $student_id;
}

if (!empty($class)) {
    $query .= ' AND s.class = ?';
    $params[] = $class;
}

$query .= ' GROUP BY s.id HAVING debt > 0';

if (!empty($debt_min) && is_numeric($debt_min)) {
    $query .= ' AND debt >= ?';
    $params[] = $debt_min;
}

$query .= ' ORDER BY debt DESC';

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$students = $stmt->fetchAll();

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_students = $_POST['selected_students'] ?? [];
    $message_template = $_POST['message_template'] ?? '';
    $notification_type = $_POST['notification_type'] ?? 'sms';

    if (empty($selected_students)) {
        $error_message = 'يرجى اختيار طالب واحد على الأقل';
    } elseif (empty($message_template)) {
        $error_message = 'يرجى إدخال نص الرسالة';
    } else {
        // In a real application, you would integrate with an SMS or email service here
        // For this demo, we'll just simulate sending notifications

        $sent_count = 0;
        foreach ($selected_students as $id) {
            // Get student details
            $stmt = $pdo->prepare('SELECT * FROM students WHERE id = ?');
            $stmt->execute([$id]);
            $student = $stmt->fetch();

            if ($student) {
                // Get student debt
                $stmt = $pdo->prepare('SELECT
                    COALESCE(SUM(CASE WHEN type = "purchase" THEN amount ELSE 0 END), 0) -
                    COALESCE(SUM(CASE WHEN type = "payment" THEN amount ELSE 0 END), 0) as debt
                    FROM transactions
                    WHERE student_id = ?');
                $stmt->execute([$id]);
                $debt_info = $stmt->fetch();
                $debt = $debt_info['debt'] ?? 0;

                // Replace placeholders in message template
                $message = str_replace(
                    ['[اسم_الطالب]', '[الصف]', '[المبلغ]', '[ولي_الأمر]'],
                    [$student['name'], $student['class'], number_format($debt, 2), $student['parent_name']],
                    $message_template
                );

                // In a real application, send the message here
                // For this demo, we'll just count it as sent
                $sent_count++;
            }
        }

        $success_message = "تم إرسال الإشعارات بنجاح إلى {$sent_count} طالب";
    }
}

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>

    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>إرسال الإشعارات</h2>
            <a href="../dashboard.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للرئيسية
            </a>
        </div>

        <?php if (!empty($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <!-- Filter form -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="">
                    <?php if ($student_id): ?>
                        <input type="hidden" name="student_id" value="<?php echo $student_id; ?>">
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="class" class="form-label">الصف</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">جميع الصفوف</option>
                                <?php foreach ($classes as $c): ?>
                                    <option value="<?php echo htmlspecialchars($c['class']); ?>" <?php echo ($class === $c['class']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($c['class']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="debt_min" class="form-label">الحد الأدنى للدين</label>
                            <input type="number" class="form-control" id="debt_min" name="debt_min" step="0.01" min="0" value="<?php echo htmlspecialchars($debt_min); ?>">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="sms" <?php echo ($notification_type === 'sms') ? 'selected' : ''; ?>>رسالة SMS</option>
                                <option value="email" <?php echo ($notification_type === 'email') ? 'selected' : ''; ?>>بريد إلكتروني</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="notifications.php" class="btn btn-light">إعادة تعيين</a>
                        <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notification form -->
        <form method="POST" action="">
            <div class="row">
                <!-- Students list -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <i class="fas fa-users me-2"></i> الطلاب المدينين
                        </div>
                        <div class="card-body">
                            <?php if (count($students) > 0): ?>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll">
                                            تحديد الكل
                                        </label>
                                    </div>
                                </div>

                                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                    <table class="table table-hover">
                                        <thead class="table-light sticky-top">
                                            <tr>
                                                <th></th>
                                                <th>الطالب</th>
                                                <th>الصف</th>
                                                <th>المبلغ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($students as $student): ?>
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input student-checkbox" type="checkbox" name="selected_students[]" value="<?php echo $student['id']; ?>" id="student_<?php echo $student['id']; ?>" <?php echo ($student_id == $student['id']) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="student_<?php echo $student['id']; ?>"></label>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['class']); ?></td>
                                                    <td class="text-danger"><?php echo number_format($student['debt'], 2); ?> دينار</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="mt-3">
                                    <p class="text-muted">تم العثور على <?php echo count($students); ?> طالب مدين</p>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    لا يوجد طلاب مدينين بالمبلغ المحدد.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Message template -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <i class="fas fa-envelope me-2"></i> نموذج الرسالة
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="notification_type" class="form-label">نوع الإشعار</label>
                                <select class="form-select" id="notification_type" name="notification_type">
                                    <option value="sms" <?php echo ($notification_type === 'sms') ? 'selected' : ''; ?>>رسالة SMS</option>
                                    <option value="email" <?php echo ($notification_type === 'email') ? 'selected' : ''; ?>>بريد إلكتروني</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="message_template" class="form-label">نص الرسالة</label>
                                <textarea class="form-control" id="message_template" name="message_template" rows="6" required><?php echo isset($_POST['message_template']) ? htmlspecialchars($_POST['message_template']) : "السيد ولي أمر الطالب [اسم_الطالب] المحترم،\n\nنود إشعاركم بأن على ابنكم مبلغ [المبلغ] دينار كدين للمقصف المدرسي. يرجى سداد المبلغ في أقرب وقت ممكن.\n\nمع الشكر،\nإدارة المدرسة"; ?></textarea>
                                <div class="form-text">
                                    يمكنك استخدام الرموز التالية في الرسالة:
                                    <ul class="mb-0">
                                        <li>[اسم_الطالب] - اسم الطالب</li>
                                        <li>[الصف] - الصف</li>
                                        <li>[المبلغ] - مبلغ الدين</li>
                                        <li>[ولي_الأمر] - اسم ولي الأمر</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-success" <?php echo (count($students) === 0) ? 'disabled' : ''; ?>>
                                    <i class="fas fa-paper-plane me-1"></i> إرسال الإشعارات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select all checkbox functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const studentCheckboxes = document.querySelectorAll('.student-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                studentCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });

            studentCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const allChecked = Array.from(studentCheckboxes).every(c => c.checked);
                    const anyChecked = Array.from(studentCheckboxes).some(c => c.checked);

                    selectAllCheckbox.checked = allChecked;
                    selectAllCheckbox.indeterminate = anyChecked && !allChecked;
                });
            });
        }
    });
</script>

<?php include '../includes/footer.php'; ?>
