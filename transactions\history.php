<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get filter parameters
$student_id = $_GET['student_id'] ?? '';
$type = $_GET['type'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query
$query = 'SELECT t.*, s.name as student_name, s.class as student_class, u.username as created_by_name
    FROM transactions t
    JOIN students s ON t.student_id = s.id
    LEFT JOIN users u ON t.created_by = u.id
    WHERE 1=1';
$params = [];

if (!empty($student_id)) {
    $query .= ' AND t.student_id = ?';
    $params[] = $student_id;
}

if (!empty($type)) {
    $query .= ' AND t.type = ?';
    $params[] = $type;
}

if (!empty($date_from)) {
    $query .= ' AND t.date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $query .= ' AND t.date <= ?';
    $params[] = $date_to;
}

$query .= ' ORDER BY t.date DESC, t.id DESC';

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$transactions = $stmt->fetchAll();

// Get all students for filter
$stmt = $pdo->query('SELECT id, name, class FROM students ORDER BY name');
$all_students = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>سجل المعاملات</h2>
            <div>
                <a href="../transactions/new.php" class="btn btn-danger me-2">
                    <i class="fas fa-shopping-cart me-1"></i> تسجيل مشتريات
                </a>
                <a href="../transactions/payment.php" class="btn btn-success">
                    <i class="fas fa-money-bill-wave me-1"></i> تسجيل دفعة
                </a>
            </div>
        </div>
        
        <!-- Filter form -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="student_id" class="form-label">الطالب</label>
                            <select class="form-select" id="student_id" name="student_id">
                                <option value="">جميع الطلاب</option>
                                <?php foreach ($all_students as $s): ?>
                                    <option value="<?php echo $s['id']; ?>" <?php echo ($student_id == $s['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($s['name'] . ' - ' . $s['class']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="type" class="form-label">نوع المعاملة</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">الكل</option>
                                <option value="purchase" <?php echo ($type === 'purchase') ? 'selected' : ''; ?>>مشتريات</option>
                                <option value="payment" <?php echo ($type === 'payment') ? 'selected' : ''; ?>>دفعات</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="history.php" class="btn btn-light">إعادة تعيين</a>
                        <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Transactions table -->
        <div class="card">
            <div class="card-body">
                <?php if (count($transactions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الطالب</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($transaction['date'])); ?></td>
                                        <td>
                                            <a href="../students/view.php?id=<?php echo $transaction['student_id']; ?>">
                                                <?php echo htmlspecialchars($transaction['student_name'] . ' - ' . $transaction['student_class']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if ($transaction['type'] === 'purchase'): ?>
                                                <span class="badge bg-danger">مشتريات</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">دفعة</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['type'] === 'purchase'): ?>
                                                <span class="text-danger"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                            <?php else: ?>
                                                <span class="text-success"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير معروف'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Summary -->
                    <?php
                    $total_purchases = 0;
                    $total_payments = 0;
                    
                    foreach ($transactions as $transaction) {
                        if ($transaction['type'] === 'purchase') {
                            $total_purchases += $transaction['amount'];
                        } else {
                            $total_payments += $transaction['amount'];
                        }
                    }
                    ?>
                    
                    <div class="card mt-3 bg-light">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <h6 class="text-muted">إجمالي المشتريات</h6>
                                    <h4 class="text-danger"><?php echo number_format($total_purchases, 2); ?> ريال</h4>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-muted">إجمالي المدفوعات</h6>
                                    <h4 class="text-success"><?php echo number_format($total_payments, 2); ?> ريال</h4>
                                </div>
                                <div class="col-md-4">
                                    <h6 class="text-muted">الرصيد</h6>
                                    <?php $balance = $total_purchases - $total_payments; ?>
                                    <?php if ($balance > 0): ?>
                                        <h4 class="text-danger"><?php echo number_format($balance, 2); ?> ريال</h4>
                                    <?php elseif ($balance < 0): ?>
                                        <h4 class="text-success"><?php echo number_format(abs($balance), 2); ?> ريال</h4>
                                    <?php else: ?>
                                        <h4>0.00 ريال</h4>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        لا توجد معاملات تطابق معايير البحث.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
