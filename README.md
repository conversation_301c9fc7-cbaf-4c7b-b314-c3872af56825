# نظام إدارة ديون المقصف المدرسي

نظام ويب متكامل لإدارة ديون المقصف المدرسي للطلاب، يتيح للمشرفين تسجيل مشتريات الطلاب اليومية، متابعة الديون، وتوليد تقارير دورية مفصلة لأولياء الأمور والإدارة.

## المميزات الرئيسية

- **إدارة الطلاب**: إضافة وتعديل بيانات الطلاب (الاسم، الصف، رقم الهوية، ولي الأمر، رقم الجوال)
- **تسجيل المشتريات**: تسجيل المشتريات اليومية للطلاب
- **تسجيل الدفعات**: تسجيل عمليات السداد للطلاب
- **متابعة الديون**: عرض ديون كل طالب وإجمالي الديون
- **التقارير**: إنشاء تقارير مفصلة قابلة للطباعة
- **الإشعارات**: إرسال إشعارات لأولياء الأمور بمبالغ الديون

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache أو Nginx)

## طريقة التثبيت

1. قم بإنشاء قاعدة بيانات MySQL جديدة باسم `canteen_management`
2. قم باستيراد ملف `database.sql` إلى قاعدة البيانات
3. قم بتعديل ملف `config/db.php` بمعلومات الاتصال بقاعدة البيانات الخاصة بك
4. انقل جميع الملفات إلى مجلد الخادم الخاص بك
5. قم بزيارة الموقع من خلال المتصفح

## بيانات الدخول الافتراضية

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

## هيكل النظام

- **الصفحة الرئيسية**: عرض ملخص للديون والطلاب والمعاملات الأخيرة
- **إدارة الطلاب**: إضافة وتعديل وعرض بيانات الطلاب
- **تسجيل المشتريات**: تسجيل مشتريات الطلاب اليومية
- **تسجيل الدفعات**: تسجيل عمليات السداد
- **سجل المعاملات**: عرض سجل المعاملات مع إمكانية التصفية
- **التقارير**: إنشاء تقارير مفصلة قابلة للطباعة
- **الإشعارات**: إرسال إشعارات لأولياء الأمور

## الأمان

- تشفير كلمات المرور باستخدام خوارزمية bcrypt
- حماية ضد هجمات SQL Injection
- التحقق من صحة البيانات المدخلة
- جلسات آمنة للمستخدمين

## الدعم والتطوير

يمكن تخصيص النظام وتطويره حسب احتياجات المدرسة، مثل:
- إضافة واجهة لأولياء الأمور
- دمج النظام مع أنظمة المدرسة الأخرى
- إضافة خدمات الدفع الإلكتروني
- تطوير تطبيق جوال للنظام

## الترخيص

هذا النظام متاح للاستخدام المجاني للمدارس والمؤسسات التعليمية.
