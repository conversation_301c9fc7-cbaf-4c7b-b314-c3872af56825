// Expenses management functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set default date to today
    document.getElementById('expense-date').valueAsDate = new Date();
    
    // Show/hide other type input based on selection
    document.getElementById('expense-type').addEventListener('change', function() {
        const otherTypeContainer = document.getElementById('other-type-container');
        if (this.value === 'أخرى') {
            otherTypeContainer.style.display = 'block';
            document.getElementById('other-type').setAttribute('required', 'required');
        } else {
            otherTypeContainer.style.display = 'none';
            document.getElementById('other-type').removeAttribute('required');
        }
    });
    
    document.getElementById('edit-expense-type').addEventListener('change', function() {
        const otherTypeContainer = document.getElementById('edit-other-type-container');
        if (this.value === 'أخرى') {
            otherTypeContainer.style.display = 'block';
            document.getElementById('edit-other-type').setAttribute('required', 'required');
        } else {
            otherTypeContainer.style.display = 'none';
            document.getElementById('edit-other-type').removeAttribute('required');
        }
    });

    // Handle save expense button
    document.getElementById('save-expense-btn').addEventListener('click', saveExpense);
    
    // Handle update expense button
    document.getElementById('update-expense-btn').addEventListener('click', updateExpense);
    
    // Handle filter buttons
    document.getElementById('apply-filters').addEventListener('click', filterExpenses);
    document.getElementById('reset-filters').addEventListener('click', resetFilters);

    // Load expenses data
    loadExpensesData();
    
    // Load expense types for filter
    loadExpenseTypes();
});

// Load expenses data
function loadExpensesData() {
    // Get data from localStorage
    const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];
    
    // Update summary
    updateExpenseSummary(expenses);
    
    // Populate expenses table
    const tableBody = document.getElementById('expenses-table-body');
    tableBody.innerHTML = '';
    
    if (expenses.length > 0) {
        // Sort expenses by date (newest first)
        expenses.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        expenses.forEach(expense => {
            // Find user who created the expense
            const user = users.find(u => u.id === expense.created_by) || { username: 'غير معروف' };
            
            const row = document.createElement('tr');
            row.className = 'expense-row';
            row.setAttribute('data-id', expense.id);
            row.setAttribute('data-type', expense.type);
            row.setAttribute('data-date', expense.date);
            row.innerHTML = `
                <td>${formatDate(expense.date)}</td>
                <td>${expense.type}</td>
                <td>${expense.description || '-'}</td>
                <td>${formatKuwaitiCurrency(expense.amount)}</td>
                <td>${user.username}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-primary edit-expense-btn" data-bs-toggle="tooltip" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger delete-expense-btn" data-bs-toggle="tooltip" title="حذف">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
            
            // Add event listeners to buttons
            row.querySelector('.edit-expense-btn').addEventListener('click', function() {
                openEditExpenseModal(expense.id);
            });
            
            row.querySelector('.delete-expense-btn').addEventListener('click', function() {
                deleteExpense(expense.id);
            });
        });
        
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" class="text-center">
                لا توجد مصروفات مسجلة حالياً.
            </td>
        `;
        tableBody.appendChild(row);
    }
}

// Update expense summary
function updateExpenseSummary(expenses) {
    let totalExpenses = 0;
    let monthExpenses = 0;
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    expenses.forEach(expense => {
        const expenseAmount = parseFloat(expense.amount);
        totalExpenses += expenseAmount;
        
        // Check if expense is in current month
        const expenseDate = new Date(expense.date);
        if (expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear) {
            monthExpenses += expenseAmount;
        }
    });
    
    document.getElementById('total-expenses').textContent = formatKuwaitiCurrency(totalExpenses);
    document.getElementById('month-expenses').textContent = formatKuwaitiCurrency(monthExpenses);
    document.getElementById('expense-count').textContent = expenses.length;
}

// Load expense types for filter
function loadExpenseTypes() {
    const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    const typeFilter = document.getElementById('expense-type-filter');
    
    // Get unique expense types
    const types = [...new Set(expenses.map(expense => expense.type))].sort();
    
    // Clear existing options except the first one
    typeFilter.innerHTML = '<option value="">جميع الأنواع</option>';
    
    // Add options for each type
    types.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        option.textContent = type;
        typeFilter.appendChild(option);
    });
}

// Save new expense
function saveExpense() {
    // Get form data
    const expenseType = document.getElementById('expense-type').value;
    const otherType = document.getElementById('other-type').value;
    const date = document.getElementById('expense-date').value;
    const amountInFils = parseFloat(document.getElementById('expense-amount').value);
    const description = document.getElementById('expense-description').value;
    
    // Validate form data
    if (!expenseType) {
        alert('يرجى اختيار نوع المصروف');
        return;
    }
    
    if (expenseType === 'أخرى' && !otherType) {
        alert('يرجى إدخال نوع المصروف الآخر');
        return;
    }
    
    if (!date) {
        alert('يرجى إدخال تاريخ المصروف');
        return;
    }
    
    if (isNaN(amountInFils) || amountInFils <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }
    
    // Get current user
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    
    // Get existing expenses from localStorage
    const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    
    // Generate new expense ID
    const newId = expenses.length > 0 ? Math.max(...expenses.map(expense => expense.id)) + 1 : 1;
    
    // Create new expense object
    const newExpense = {
        id: newId,
        type: expenseType === 'أخرى' ? otherType : expenseType,
        amount: amountInFils / 1000, // تحويل من فلس إلى دينار
        description: description,
        date: date,
        created_by: currentUser.id,
        created_at: new Date().toISOString()
    };
    
    // Add new expense to array
    expenses.push(newExpense);
    
    // Save updated expenses array to localStorage
    localStorage.setItem('expenses', JSON.stringify(expenses));
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addExpenseModal'));
    modal.hide();
    
    // Reset form
    document.getElementById('add-expense-form').reset();
    document.getElementById('expense-date').valueAsDate = new Date();
    document.getElementById('other-type-container').style.display = 'none';
    
    // Show success message
    alert('تم إضافة المصروف بنجاح');
    
    // Reload expenses data
    loadExpensesData();
    
    // Reload expense types for filter
    loadExpenseTypes();
}

// Open edit expense modal
function openEditExpenseModal(expenseId) {
    // Get expenses from localStorage
    const expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    
    // Find expense
    const expense = expenses.find(e => e.id === expenseId);
    
    if (!expense) {
        alert('لم يتم العثور على المصروف');
        return;
    }
    
    // Set form values
    document.getElementById('edit-expense-id').value = expense.id;
    
    // Check if expense type is in predefined list
    const predefinedTypes = ['مشتريات', 'رواتب', 'صيانة', 'فواتير', 'إيجار'];
    if (predefinedTypes.includes(expense.type)) {
        document.getElementById('edit-expense-type').value = expense.type;
        document.getElementById('edit-other-type-container').style.display = 'none';
    } else {
        document.getElementById('edit-expense-type').value = 'أخرى';
        document.getElementById('edit-other-type').value = expense.type;
        document.getElementById('edit-other-type-container').style.display = 'block';
    }
    
    document.getElementById('edit-expense-date').value = expense.date;
    document.getElementById('edit-expense-amount').value = Math.round(expense.amount * 1000); // تحويل من دينار إلى فلس
    document.getElementById('edit-expense-description').value = expense.description || '';
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editExpenseModal'));
    modal.show();
}

// Update expense
function updateExpense() {
    // Get form data
    const expenseId = parseInt(document.getElementById('edit-expense-id').value);
    const expenseType = document.getElementById('edit-expense-type').value;
    const otherType = document.getElementById('edit-other-type').value;
    const date = document.getElementById('edit-expense-date').value;
    const amountInFils = parseFloat(document.getElementById('edit-expense-amount').value);
    const description = document.getElementById('edit-expense-description').value;
    
    // Validate form data
    if (!expenseType) {
        alert('يرجى اختيار نوع المصروف');
        return;
    }
    
    if (expenseType === 'أخرى' && !otherType) {
        alert('يرجى إدخال نوع المصروف الآخر');
        return;
    }
    
    if (!date) {
        alert('يرجى إدخال تاريخ المصروف');
        return;
    }
    
    if (isNaN(amountInFils) || amountInFils <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }
    
    // Get expenses from localStorage
    let expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    
    // Find expense index
    const expenseIndex = expenses.findIndex(e => e.id === expenseId);
    
    if (expenseIndex === -1) {
        alert('لم يتم العثور على المصروف');
        return;
    }
    
    // Update expense
    expenses[expenseIndex] = {
        ...expenses[expenseIndex],
        type: expenseType === 'أخرى' ? otherType : expenseType,
        amount: amountInFils / 1000, // تحويل من فلس إلى دينار
        description: description,
        date: date
    };
    
    // Save updated expenses array to localStorage
    localStorage.setItem('expenses', JSON.stringify(expenses));
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('editExpenseModal'));
    modal.hide();
    
    // Show success message
    alert('تم تحديث المصروف بنجاح');
    
    // Reload expenses data
    loadExpensesData();
    
    // Reload expense types for filter
    loadExpenseTypes();
}

// Delete expense
function deleteExpense(expenseId) {
    // Confirm deletion
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
        return;
    }
    
    // Get expenses from localStorage
    let expenses = JSON.parse(localStorage.getItem('expenses')) || [];
    
    // Find expense
    const expenseIndex = expenses.findIndex(e => e.id === expenseId);
    
    if (expenseIndex === -1) {
        alert('لم يتم العثور على المصروف');
        return;
    }
    
    // Remove expense
    expenses.splice(expenseIndex, 1);
    
    // Save updated expenses array to localStorage
    localStorage.setItem('expenses', JSON.stringify(expenses));
    
    // Show success message
    alert('تم حذف المصروف بنجاح');
    
    // Reload expenses data
    loadExpensesData();
    
    // Reload expense types for filter
    loadExpenseTypes();
}

// Filter expenses
function filterExpenses() {
    const typeFilter = document.getElementById('expense-type-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    
    const rows = document.querySelectorAll('.expense-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const rowType = row.getAttribute('data-type');
        const rowDate = row.getAttribute('data-date');
        
        // Check if matches type filter
        const matchesType = !typeFilter || rowType === typeFilter;
        
        // Check if matches date range
        let matchesDateRange = true;
        if (dateFrom && rowDate < dateFrom) {
            matchesDateRange = false;
        }
        if (dateTo && rowDate > dateTo) {
            matchesDateRange = false;
        }
        
        // Show/hide row based on filters
        if (matchesType && matchesDateRange) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // Show message if no results
    const tableBody = document.getElementById('expenses-table-body');
    const noResultsRow = document.getElementById('no-results-row');
    
    if (visibleCount === 0 && !noResultsRow) {
        const row = document.createElement('tr');
        row.id = 'no-results-row';
        row.innerHTML = `
            <td colspan="6" class="text-center">
                لا توجد نتائج مطابقة لمعايير البحث
            </td>
        `;
        tableBody.appendChild(row);
    } else if (visibleCount > 0 && noResultsRow) {
        noResultsRow.remove();
    }
}

// Reset filters
function resetFilters() {
    document.getElementById('expense-type-filter').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    
    // Show all rows
    document.querySelectorAll('.expense-row').forEach(row => {
        row.style.display = '';
    });
    
    // Remove no results message if exists
    const noResultsRow = document.getElementById('no-results-row');
    if (noResultsRow) {
        noResultsRow.remove();
    }
}
