<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل المعاملات - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="css/dark-mode.css">
    <!-- Print CSS -->
    <link rel="stylesheet" href="css/print.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="whatsapp-settings.html" class="list-group-item list-group-item-action">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>سجل المعاملات</h2>
                    <div>
                        <a href="new-transaction.html" class="btn btn-danger me-2">
                            <i class="fas fa-shopping-cart me-1"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="btn btn-success">
                            <i class="fas fa-money-bill-wave me-1"></i> تسجيل دفعة
                        </a>
                    </div>
                </div>

                <!-- Filter form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="transaction-filter-form">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="student_id" class="form-label">الطالب</label>
                                    <select class="form-select" id="student_id">
                                        <option value="">جميع الطلاب</option>
                                        <!-- Will be populated by JavaScript -->
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="type" class="form-label">نوع المعاملة</label>
                                    <select class="form-select" id="type">
                                        <option value="">الكل</option>
                                        <option value="purchase">مشتريات</option>
                                        <option value="payment">دفعات</option>
                                    </select>
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from">
                                </div>

                                <div class="col-md-3 mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to">
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="reset" class="btn btn-light" id="reset-filter">إعادة تعيين</button>
                                <div>
                                    <button type="button" class="btn btn-secondary me-2" id="print-btn">
                                        <i class="fas fa-print me-1"></i> طباعة
                                    </button>
                                    <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Transactions table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الطالب</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                        <th>بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table-body">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary -->
                        <div class="card mt-3 bg-light">
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <h6 class="text-muted">إجمالي المشتريات</h6>
                                        <h4 class="text-danger" id="total-purchases">0.00 ريال</h4>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-muted">إجمالي المدفوعات</h6>
                                        <h4 class="text-success" id="total-payments">0.00 ريال</h4>
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="text-muted">الرصيد</h6>
                                        <h4 id="balance">0.00 ريال</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Print Footer - Only visible when printing -->
    <div class="print-footer print-only" style="display: none;">
        <p>صفحة <span class="page-number"></span> - &copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/print-report.js"></script>
    <script src="js/transactions.js"></script>
    <script src="js/dark-mode.js"></script>
</body>
</html>
