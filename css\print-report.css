/* Print Report Styles */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    direction: rtl;
}

.report-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #000;
}

.report-header h1 {
    margin: 0;
    font-size: 24px;
    margin-bottom: 5px;
}

.report-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: normal;
}

.report-header .logo {
    max-width: 100px;
    margin-bottom: 10px;
}

.report-info {
    margin-bottom: 20px;
}

.report-info table {
    width: 100%;
    border-collapse: collapse;
}

.report-info table td {
    padding: 5px;
}

.report-info table td:first-child {
    font-weight: bold;
    width: 150px;
}

.report-content {
    margin-bottom: 20px;
}

.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.report-table th, 
.report-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: right;
}

.report-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

.report-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.report-summary {
    margin-top: 20px;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.report-summary table {
    width: 300px;
    margin-right: auto;
    border-collapse: collapse;
}

.report-summary table td {
    padding: 5px;
}

.report-summary table td:first-child {
    font-weight: bold;
}

.report-footer {
    margin-top: 30px;
    text-align: center;
    font-size: 12px;
    color: #666;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.text-danger {
    color: #dc3545;
}

.text-success {
    color: #28a745;
}

.badge {
    display: inline-block;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 10px;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

@media print {
    body {
        padding: 0;
        margin: 0;
    }
    
    @page {
        margin: 1.5cm;
    }
}
