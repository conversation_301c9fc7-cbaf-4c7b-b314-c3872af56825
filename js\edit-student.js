// Edit student functionality

// Global variables
let studentId;
let currentUser;

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }
    
    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });
    
    // Get student ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    studentId = urlParams.get('id');
    
    if (!studentId) {
        alert('معرف الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }
    
    // Load student data
    loadStudentData(studentId);
    
    // Setup form submission
    document.getElementById('edit-student-form').addEventListener('submit', function(e) {
        e.preventDefault();
        updateStudent();
    });
    
    // Setup cancel button
    document.getElementById('cancel-btn').addEventListener('click', function() {
        window.location.href = `student-details.html?id=${studentId}`;
    });
    
    // Setup back button
    document.getElementById('back-to-details-btn').addEventListener('click', function(e) {
        e.preventDefault();
        window.location.href = `student-details.html?id=${studentId}`;
    });
    
    // Setup print functionality
    setupPrintFunctionality();
});

// Load student data
function loadStudentData(studentId) {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    
    // Find student by ID
    const student = students.find(s => s.id == studentId);
    
    if (!student) {
        alert('الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }
    
    // Populate form fields
    document.getElementById('student-name').value = student.name;
    document.getElementById('student-id-number').value = student.student_id;
    document.getElementById('student-class').value = student.class;
    document.getElementById('parent-name').value = student.parent_name;
    document.getElementById('phone-number').value = student.phone_number;
    
    // Optional fields
    if (student.email) {
        document.getElementById('email').value = student.email;
    }
    
    if (student.address) {
        document.getElementById('address').value = student.address;
    }
    
    if (student.notes) {
        document.getElementById('notes').value = student.notes;
    }
}

// Update student
function updateStudent() {
    // Get form values
    const name = document.getElementById('student-name').value;
    const studentIdNumber = document.getElementById('student-id-number').value;
    const studentClass = document.getElementById('student-class').value;
    const parentName = document.getElementById('parent-name').value;
    const phoneNumber = document.getElementById('phone-number').value;
    const email = document.getElementById('email').value;
    const address = document.getElementById('address').value;
    const notes = document.getElementById('notes').value;
    
    // Validate form
    if (!name || !studentIdNumber || !studentClass || !parentName || !phoneNumber) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Get students from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    
    // Find student index
    const studentIndex = students.findIndex(s => s.id == studentId);
    
    if (studentIndex === -1) {
        alert('الطالب غير موجود');
        return;
    }
    
    // Check if student ID is already used by another student
    const isDuplicateId = students.some(s => s.student_id === studentIdNumber && s.id != studentId);
    
    if (isDuplicateId) {
        alert('رقم الهوية مستخدم بالفعل لطالب آخر');
        return;
    }
    
    // Update student data
    students[studentIndex].name = name;
    students[studentIndex].student_id = studentIdNumber;
    students[studentIndex].class = studentClass;
    students[studentIndex].parent_name = parentName;
    students[studentIndex].phone_number = phoneNumber;
    students[studentIndex].email = email;
    students[studentIndex].address = address;
    students[studentIndex].notes = notes;
    students[studentIndex].updated_at = new Date().toISOString();
    students[studentIndex].updated_by = currentUser ? currentUser.id : 1;
    
    // Save to localStorage
    localStorage.setItem('students', JSON.stringify(students));
    
    // Show success message
    alert('تم تحديث بيانات الطالب بنجاح');
    
    // Redirect to student details page
    window.location.href = `student-details.html?id=${studentId}`;
}

// Setup print functionality
function setupPrintFunctionality() {
    // Set print date
    const now = new Date();
    const formattedDate = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
    
    // Add print button event listener
    document.getElementById('print-btn').addEventListener('click', function() {
        // Add page title for printing
        const pageTitle = document.createElement('div');
        pageTitle.className = 'page-title print-only';
        pageTitle.style.display = 'none';
        pageTitle.innerHTML = '<h2>تعديل بيانات الطالب - نظام إدارة ديون المقصف المدرسي</h2>';
        document.body.insertBefore(pageTitle, document.body.firstChild);
        
        // Print the page
        window.print();
        
        // Remove page title after printing
        setTimeout(() => {
            pageTitle.remove();
        }, 1000);
    });
}
