<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get student ID from URL if provided
$student_id = $_GET['student_id'] ?? null;

// Get filter parameters
$class = $_GET['class'] ?? '';
$debt_min = $_GET['debt_min'] ?? '';
$report_type = $_GET['report_type'] ?? 'all';

// Get all classes for filter
$stmt = $pdo->query('SELECT DISTINCT class FROM students ORDER BY class');
$classes = $stmt->fetchAll();

// Build query
$query = 'SELECT s.*, 
    COALESCE(SUM(CASE WHEN t.type = "purchase" THEN t.amount ELSE 0 END), 0) as total_purchases, 
    COALESCE(SUM(CASE WHEN t.type = "payment" THEN t.amount ELSE 0 END), 0) as total_payments,
    COALESCE(SUM(CASE WHEN t.type = "purchase" THEN t.amount ELSE 0 END), 0) - 
    COALESCE(SUM(CASE WHEN t.type = "payment" THEN t.amount ELSE 0 END), 0) as debt
    FROM students s
    LEFT JOIN transactions t ON s.id = t.student_id
    WHERE 1=1';
$params = [];

if (!empty($student_id)) {
    $query .= ' AND s.id = ?';
    $params[] = $student_id;
}

if (!empty($class)) {
    $query .= ' AND s.class = ?';
    $params[] = $class;
}

$query .= ' GROUP BY s.id';

if ($report_type === 'debt_only') {
    $query .= ' HAVING debt > 0';
    
    if (!empty($debt_min) && is_numeric($debt_min)) {
        $query .= ' AND debt >= ?';
        $params[] = $debt_min;
    }
} elseif ($report_type === 'credit_only') {
    $query .= ' HAVING debt < 0';
}

$query .= ' ORDER BY s.class, s.name';

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$students = $stmt->fetchAll();

// Calculate totals
$total_debt = 0;
$total_credit = 0;

foreach ($students as $student) {
    if ($student['debt'] > 0) {
        $total_debt += $student['debt'];
    } else {
        $total_credit += abs($student['debt']);
    }
}

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>إنشاء التقارير</h2>
            <div>
                <button id="printReport" class="btn btn-info me-2">
                    <i class="fas fa-print me-1"></i> طباعة التقرير
                </button>
                <a href="../dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للرئيسية
                </a>
            </div>
        </div>
        
        <!-- Filter form -->
        <div class="card mb-4 no-print">
            <div class="card-body">
                <form method="GET" action="">
                    <?php if ($student_id): ?>
                        <input type="hidden" name="student_id" value="<?php echo $student_id; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="class" class="form-label">الصف</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">جميع الصفوف</option>
                                <?php foreach ($classes as $c): ?>
                                    <option value="<?php echo htmlspecialchars($c['class']); ?>" <?php echo ($class === $c['class']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($c['class']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="all" <?php echo ($report_type === 'all') ? 'selected' : ''; ?>>جميع الطلاب</option>
                                <option value="debt_only" <?php echo ($report_type === 'debt_only') ? 'selected' : ''; ?>>الطلاب المدينين فقط</option>
                                <option value="credit_only" <?php echo ($report_type === 'credit_only') ? 'selected' : ''; ?>>الطلاب ذوي الرصيد فقط</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="debt_min" class="form-label">الحد الأدنى للدين</label>
                            <input type="number" class="form-control" id="debt_min" name="debt_min" step="0.01" min="0" value="<?php echo htmlspecialchars($debt_min); ?>">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="generate.php" class="btn btn-light">إعادة تعيين</a>
                        <button type="submit" class="btn btn-primary">إنشاء التقرير</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Report -->
        <div class="card">
            <div class="card-body">
                <div class="text-center mb-4">
                    <h3>تقرير ديون المقصف المدرسي</h3>
                    <p class="text-muted">تاريخ التقرير: <?php echo date('d/m/Y'); ?></p>
                    <?php if (!empty($class)): ?>
                        <h5>الصف: <?php echo htmlspecialchars($class); ?></h5>
                    <?php endif; ?>
                </div>
                
                <?php if (count($students) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الطالب</th>
                                    <th>الصف</th>
                                    <th>ولي الأمر</th>
                                    <th>رقم الجوال</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>إجمالي المدفوعات</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $counter = 1; ?>
                                <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo htmlspecialchars($student['name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['class']); ?></td>
                                        <td><?php echo htmlspecialchars($student['parent_name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['phone_number']); ?></td>
                                        <td class="text-end"><?php echo number_format($student['total_purchases'], 2); ?> ريال</td>
                                        <td class="text-end"><?php echo number_format($student['total_payments'], 2); ?> ريال</td>
                                        <td class="text-end">
                                            <?php if ($student['debt'] > 0): ?>
                                                <span class="text-danger"><?php echo number_format($student['debt'], 2); ?> ريال</span>
                                            <?php elseif ($student['debt'] < 0): ?>
                                                <span class="text-success"><?php echo number_format(abs($student['debt']), 2); ?> ريال (رصيد)</span>
                                            <?php else: ?>
                                                <span class="text-muted">0.00 ريال</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="7" class="text-end">إجمالي الديون:</th>
                                    <th class="text-end text-danger"><?php echo number_format($total_debt, 2); ?> ريال</th>
                                </tr>
                                <tr>
                                    <th colspan="7" class="text-end">إجمالي الأرصدة:</th>
                                    <th class="text-end text-success"><?php echo number_format($total_credit, 2); ?> ريال</th>
                                </tr>
                                <tr>
                                    <th colspan="7" class="text-end">الرصيد الإجمالي:</th>
                                    <th class="text-end <?php echo ($total_debt - $total_credit > 0) ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo number_format(abs($total_debt - $total_credit), 2); ?> ريال
                                        <?php echo ($total_debt - $total_credit > 0) ? '(دين)' : '(رصيد)'; ?>
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        لا توجد بيانات تطابق معايير البحث.
                    </div>
                <?php endif; ?>
                
                <div class="mt-4 text-center">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة ديون المقصف المدرسي</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    @media print {
        .no-print {
            display: none !important;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
        }
        
        .card {
            border: none !important;
        }
        
        .card-body {
            padding: 0 !important;
        }
    }
</style>

<?php include '../includes/footer.php'; ?>
