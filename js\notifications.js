// Notifications management functionality

// Initialize notifications in localStorage if not exists
if (!localStorage.getItem('notifications')) {
    localStorage.setItem('notifications', JSON.stringify([]));
}

// Global variables
let currentPage = 1;
const itemsPerPage = 10;
let filteredNotifications = [];
let currentUser;

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Load students for dropdown
    loadStudentsDropdown();

    // Load classes for dropdown
    loadClassesDropdown();

    // Load notifications
    loadNotifications();

    // Setup event listeners
    setupEventListeners();
});

// Load students dropdown
function loadStudentsDropdown() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const studentSelect = document.getElementById('notification-student');

    // Sort students by name
    students.sort((a, b) => a.name.localeCompare(b.name));

    // Clear existing options except the first one
    studentSelect.innerHTML = '<option value="">اختر الطالب...</option>';

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        studentSelect.appendChild(option);
    });
}

// Load classes dropdown
function loadClassesDropdown() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const classSelect = document.getElementById('notification-class');

    // Get unique classes
    const classes = [...new Set(students.map(student => student.class))].sort();

    // Clear existing options except the first one
    classSelect.innerHTML = '<option value="">اختر الصف...</option>';

    classes.forEach(className => {
        const option = document.createElement('option');
        option.value = className;
        option.textContent = className;
        classSelect.appendChild(option);
    });
}

// Setup event listeners
function setupEventListeners() {
    // Notification type change
    document.getElementById('notification-type').addEventListener('change', function() {
        const type = this.value;
        const studentContainer = document.getElementById('student-selection-container');
        const classContainer = document.getElementById('class-selection-container');

        if (type === 'custom') {
            studentContainer.style.display = 'block';
            classContainer.style.display = 'block';
        } else {
            studentContainer.style.display = 'block';
            classContainer.style.display = 'none';
        }
    });

    // Create notification button
    document.getElementById('create-notification-btn').addEventListener('click', createNotification);

    // Generate automatic notifications button
    document.getElementById('generate-notifications-btn').addEventListener('click', generateAutomaticNotifications);

    // Mark all as read button
    document.getElementById('mark-all-read-btn').addEventListener('click', markAllAsRead);

    // Filter change events
    document.getElementById('notification-type-filter').addEventListener('change', applyFilters);
    document.getElementById('notification-status-filter').addEventListener('change', applyFilters);
    document.getElementById('notification-date-filter').addEventListener('change', applyFilters);
}

// Load notifications
function loadNotifications() {
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];

    // Sort notifications by date (newest first)
    notifications.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // Apply filters
    applyFilters();
}

// Apply filters to notifications
function applyFilters() {
    const typeFilter = document.getElementById('notification-type-filter').value;
    const statusFilter = document.getElementById('notification-status-filter').value;
    const dateFilter = document.getElementById('notification-date-filter').value;

    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];

    // Filter by type
    filteredNotifications = notifications.filter(notification => {
        if (typeFilter === 'all') return true;
        return notification.type === typeFilter;
    });

    // Filter by status
    filteredNotifications = filteredNotifications.filter(notification => {
        if (statusFilter === 'all') return true;
        return statusFilter === 'read' ? notification.read : !notification.read;
    });

    // Filter by date
    filteredNotifications = filteredNotifications.filter(notification => {
        if (dateFilter === 'all') return true;

        const notificationDate = new Date(notification.created_at);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (dateFilter === 'today') {
            return notificationDate >= today;
        } else if (dateFilter === 'week') {
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay());
            return notificationDate >= weekStart;
        } else if (dateFilter === 'month') {
            const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
            return notificationDate >= monthStart;
        }

        return true;
    });

    // Reset to first page
    currentPage = 1;

    // Display notifications
    displayNotifications();

    // Update pagination
    updatePagination();
}

// Display notifications
function displayNotifications() {
    const notificationsList = document.getElementById('notifications-list');

    // Calculate pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex);

    // Clear list
    notificationsList.innerHTML = '';

    if (paginatedNotifications.length > 0) {
        paginatedNotifications.forEach(notification => {
            const notificationItem = document.createElement('a');
            notificationItem.href = '#';
            notificationItem.className = `list-group-item list-group-item-action ${notification.read ? '' : 'fw-bold'}`;
            notificationItem.setAttribute('data-id', notification.id);

            // Priority indicator
            let priorityBadge = '';
            if (notification.priority === 'high') {
                priorityBadge = '<span class="badge bg-warning ms-2">هام</span>';
            } else if (notification.priority === 'urgent') {
                priorityBadge = '<span class="badge bg-danger ms-2">عاجل</span>';
            }

            // Type indicator
            let typeIcon = '';
            switch (notification.type) {
                case 'debt':
                    typeIcon = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>';
                    break;
                case 'installment':
                    typeIcon = '<i class="fas fa-calendar-alt text-primary me-2"></i>';
                    break;
                case 'payment':
                    typeIcon = '<i class="fas fa-money-bill-wave text-success me-2"></i>';
                    break;
                case 'system':
                    typeIcon = '<i class="fas fa-cog text-secondary me-2"></i>';
                    break;
                case 'custom':
                    typeIcon = '<i class="fas fa-bell text-info me-2"></i>';
                    break;
                default:
                    typeIcon = '<i class="fas fa-bell text-info me-2"></i>';
            }

            // Format date
            const date = new Date(notification.created_at);
            const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;

            // Message delivery icons
            let deliveryIcons = '';
            if (notification.sms_sent) {
                deliveryIcons += '<i class="fas fa-sms text-success ms-1" data-bs-toggle="tooltip" title="تم إرسال رسالة نصية"></i>';
            }
            if (notification.whatsapp_sent) {
                deliveryIcons += '<i class="fab fa-whatsapp text-success ms-1" data-bs-toggle="tooltip" title="تم إرسال رسالة عبر واتساب"></i>';
            }

            notificationItem.innerHTML = `
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <div>
                        ${typeIcon}
                        <span>${notification.title}</span>
                        ${priorityBadge}
                        ${deliveryIcons}
                    </div>
                    <small class="text-muted">${formattedDate}</small>
                </div>
                <p class="mb-1 text-truncate ps-4">${notification.message}</p>
                ${notification.student_name ? `<small class="text-muted ps-4">الطالب: ${notification.student_name}</small>` : ''}
            `;

            notificationsList.appendChild(notificationItem);

            // Add click event
            notificationItem.addEventListener('click', function(e) {
                e.preventDefault();
                viewNotificationDetails(notification.id);
            });
        });
    } else {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'text-center p-5';
        emptyMessage.innerHTML = `
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <p>لا توجد إشعارات متطابقة مع معايير التصفية</p>
        `;
        notificationsList.appendChild(emptyMessage);
    }
}

// Update pagination
function updatePagination() {
    const paginationElement = document.getElementById('notifications-pagination');
    paginationElement.innerHTML = '';

    const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage);

    if (totalPages <= 1) {
        return;
    }

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.innerHTML = '&laquo;';
    prevLink.setAttribute('aria-label', 'Previous');
    prevLi.appendChild(prevLink);
    paginationElement.appendChild(prevLi);

    prevLink.addEventListener('click', function(e) {
        e.preventDefault();
        if (currentPage > 1) {
            currentPage--;
            displayNotifications();
            updatePagination();
        }
    });

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${currentPage === i ? 'active' : ''}`;
        const pageLink = document.createElement('a');
        pageLink.className = 'page-link';
        pageLink.href = '#';
        pageLink.textContent = i;
        pageLi.appendChild(pageLink);
        paginationElement.appendChild(pageLi);

        pageLink.addEventListener('click', function(e) {
            e.preventDefault();
            currentPage = i;
            displayNotifications();
            updatePagination();
        });
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.innerHTML = '&raquo;';
    nextLink.setAttribute('aria-label', 'Next');
    nextLi.appendChild(nextLink);
    paginationElement.appendChild(nextLi);

    nextLink.addEventListener('click', function(e) {
        e.preventDefault();
        if (currentPage < totalPages) {
            currentPage++;
            displayNotifications();
            updatePagination();
        }
    });
}

// Create notification
function createNotification() {
    const type = document.getElementById('notification-type').value;
    const studentId = document.getElementById('notification-student').value;
    const classValue = document.getElementById('notification-class').value;
    const title = document.getElementById('notification-title').value;
    const message = document.getElementById('notification-message').value;
    const priority = document.getElementById('notification-priority').value;
    const sendSMS = document.getElementById('notification-send-sms').checked;
    const sendWhatsApp = document.getElementById('notification-send-whatsapp').checked;

    // Validate form
    if (!type || !title || !message) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (type !== 'system' && !studentId && !classValue) {
        alert('يرجى اختيار طالب أو صف');
        return;
    }

    // Get students
    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Create notifications
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];

    if (classValue) {
        // Create notification for all students in class
        const classStudents = students.filter(student => student.class === classValue);

        classStudents.forEach(student => {
            createSingleNotification(type, student.id, student.name, title, message, priority, sendSMS, sendWhatsApp);
        });

        alert(`تم إنشاء ${classStudents.length} إشعار لطلاب الصف ${classValue}`);
    } else if (studentId) {
        // Create notification for single student
        const student = students.find(s => s.id == studentId);

        if (student) {
            createSingleNotification(type, student.id, student.name, title, message, priority, sendSMS, sendWhatsApp);
            alert('تم إنشاء الإشعار بنجاح');
        } else {
            alert('الطالب غير موجود');
        }
    } else {
        // Create system notification
        const notification = {
            id: Date.now(),
            type: 'system',
            title: title,
            message: message,
            priority: priority,
            read: false,
            created_at: new Date().toISOString(),
            created_by: currentUser ? currentUser.id : 1
        };

        notifications.push(notification);
        localStorage.setItem('notifications', JSON.stringify(notifications));

        alert('تم إنشاء الإشعار بنجاح');
    }

    // Close modal and reset form
    try {
        const modalElement = document.getElementById('createNotificationModal');
        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.hide();
    } catch (error) {
        console.error('Error closing modal:', error);
    }
    document.getElementById('create-notification-form').reset();

    // Reload notifications
    loadNotifications();
}

// Create single notification
function createSingleNotification(type, studentId, studentName, title, message, priority, sendSMS, sendWhatsApp) {
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const student = students.find(s => s.id == studentId);

    const notification = {
        id: Date.now() + Math.floor(Math.random() * 1000), // Ensure unique ID
        type: type,
        student_id: parseInt(studentId),
        student_name: studentName,
        title: title,
        message: message,
        priority: priority,
        read: false,
        sms_sent: sendSMS,
        whatsapp_sent: sendWhatsApp,
        created_at: new Date().toISOString(),
        created_by: currentUser ? currentUser.id : 1
    };

    notifications.push(notification);
    localStorage.setItem('notifications', JSON.stringify(notifications));

    // If student exists and has phone number
    if (student && student.phone_number) {
        // If SMS should be sent, simulate sending SMS
        if (sendSMS) {
            console.log(`SMS would be sent to ${studentName}'s parent at ${student.phone_number} with message: ${title}`);
            // In a real system, this would call an SMS API

            // Simulate SMS sending success
            notification.sms_status = 'sent';
        }

        // If WhatsApp should be sent, simulate sending WhatsApp
        if (sendWhatsApp) {
            console.log(`WhatsApp would be sent to ${studentName}'s parent at ${student.phone_number} with message: ${title}`);
            // In a real system, this would call a WhatsApp API (like Twilio or WhatsApp Business API)

            // Generate WhatsApp deep link
            const whatsappLink = generateWhatsAppLink(student.phone_number, `${title}\n\n${message}`);
            notification.whatsapp_link = whatsappLink;
            notification.whatsapp_status = 'sent';

            // Open WhatsApp link in new tab if in development mode
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                window.open(whatsappLink, '_blank');
            }
        }

        // Update notification in storage
        localStorage.setItem('notifications', JSON.stringify(notifications));
    } else if (sendSMS || sendWhatsApp) {
        console.warn(`Cannot send message to ${studentName}: No phone number available`);
    }
}

// Generate WhatsApp deep link
function generateWhatsAppLink(phoneNumber, message) {
    // Remove any non-numeric characters from phone number
    const cleanPhone = phoneNumber.replace(/\D/g, '');

    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);

    // Generate WhatsApp deep link
    return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
}

// Generate automatic notifications
function generateAutomaticNotifications() {
    // Get data
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];

    let generatedCount = 0;

    // Check for overdue debts
    students.forEach(student => {
        // Calculate student debt
        let debt = 0;

        transactions.forEach(transaction => {
            if (transaction.student_id == student.id) {
                if (transaction.type === 'purchase') {
                    debt += parseFloat(transaction.amount);
                } else if (transaction.type === 'payment') {
                    debt -= parseFloat(transaction.amount);
                }
            }
        });

        // If debt is significant, create notification
        if (debt > 5) { // More than 5 KWD
            createSingleNotification(
                'debt',
                student.id,
                student.name,
                'دين متأخر',
                `لديك دين متأخر بقيمة ${debt.toFixed(3)} د.ك. يرجى المبادرة بالسداد في أقرب وقت ممكن.`,
                'high',
                false,
                false
            );
            generatedCount++;
        }
    });

    // Check for upcoming installments
    installmentPlans.forEach(plan => {
        if (plan.status === 'active') {
            // Find next pending installment
            const pendingInstallment = plan.installments.find(installment => installment.status === 'pending');

            if (pendingInstallment) {
                const dueDate = new Date(pendingInstallment.due_date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                const diffTime = dueDate - today;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                // If due date is within 3 days, create notification
                if (diffDays >= 0 && diffDays <= 3) {
                    createSingleNotification(
                        'installment',
                        plan.student_id,
                        plan.student_name,
                        'قسط مستحق قريباً',
                        `لديك قسط مستحق بتاريخ ${formatDate(pendingInstallment.due_date)} بقيمة ${pendingInstallment.amount.toFixed(3)} د.ك. يرجى الالتزام بموعد السداد.`,
                        diffDays === 0 ? 'urgent' : 'high',
                        false,
                        false
                    );
                    generatedCount++;
                }
                // If installment is overdue, create notification
                else if (diffDays < 0) {
                    createSingleNotification(
                        'installment',
                        plan.student_id,
                        plan.student_name,
                        'قسط متأخر',
                        `لديك قسط متأخر بتاريخ ${formatDate(pendingInstallment.due_date)} بقيمة ${pendingInstallment.amount.toFixed(3)} د.ك. يرجى المبادرة بالسداد فوراً.`,
                        'urgent',
                        false,
                        false
                    );
                    generatedCount++;
                }
            }
        }
    });

    // Check for recent payments
    const recentPayments = transactions.filter(transaction => {
        if (transaction.type !== 'payment') return false;

        const transactionDate = new Date(transaction.date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Check if payment was made today
        return transactionDate >= today;
    });

    recentPayments.forEach(payment => {
        const student = students.find(s => s.id == payment.student_id);

        if (student) {
            createSingleNotification(
                'payment',
                student.id,
                student.name,
                'تم استلام دفعة',
                `تم استلام دفعة بقيمة ${parseFloat(payment.amount).toFixed(3)} د.ك بتاريخ ${formatDate(payment.date)}. شكراً لسدادكم.`,
                'normal',
                false,
                false
            );
            generatedCount++;
        }
    });

    // Reload notifications
    loadNotifications();

    alert(`تم توليد ${generatedCount} إشعار تلقائي`);
}

// View notification details
function viewNotificationDetails(notificationId) {
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];
    const notification = notifications.find(n => n.id == notificationId);

    if (!notification) {
        alert('الإشعار غير موجود');
        return;
    }

    // Mark as read
    if (!notification.read) {
        notification.read = true;
        localStorage.setItem('notifications', JSON.stringify(notifications));

        // Update UI
        const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
        if (notificationElement) {
            notificationElement.classList.remove('fw-bold');
        }
    }

    // Get notification details container
    const detailsContainer = document.getElementById('notification-details-content');

    // Format date
    const date = new Date(notification.created_at);
    const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;

    // Priority badge
    let priorityBadge = '';
    if (notification.priority === 'high') {
        priorityBadge = '<span class="badge bg-warning ms-2">هام</span>';
    } else if (notification.priority === 'urgent') {
        priorityBadge = '<span class="badge bg-danger ms-2">عاجل</span>';
    }

    // Type badge
    let typeBadge = '';
    switch (notification.type) {
        case 'debt':
            typeBadge = '<span class="badge bg-warning">دين متأخر</span>';
            break;
        case 'installment':
            typeBadge = '<span class="badge bg-primary">قسط مستحق</span>';
            break;
        case 'payment':
            typeBadge = '<span class="badge bg-success">دفعة مستلمة</span>';
            break;
        case 'system':
            typeBadge = '<span class="badge bg-secondary">إشعار نظام</span>';
            break;
        case 'custom':
            typeBadge = '<span class="badge bg-info">إشعار مخصص</span>';
            break;
    }

    // Create details HTML
    detailsContainer.innerHTML = `
        <div class="mb-3">
            <h5 class="d-flex align-items-center">
                ${notification.title}
                ${priorityBadge}
            </h5>
            <div class="text-muted mb-2">
                ${typeBadge}
                <small class="ms-2">${formattedDate}</small>
            </div>
            ${notification.student_name ? `<div class="mb-2"><strong>الطالب:</strong> ${notification.student_name}</div>` : ''}
            <div class="alert alert-light">
                ${notification.message}
            </div>
            ${notification.sms_sent ? '<div class="text-success mb-1"><i class="fas fa-sms me-1"></i> تم إرسال رسالة نصية</div>' : ''}
            ${notification.whatsapp_sent ? `
                <div class="text-success">
                    <i class="fab fa-whatsapp me-1"></i> تم إرسال رسالة عبر واتساب
                    ${notification.whatsapp_link ? `
                        <a href="${notification.whatsapp_link}" target="_blank" class="btn btn-sm btn-success ms-2">
                            <i class="fab fa-whatsapp"></i> فتح واتساب
                        </a>
                    ` : ''}
                </div>
            ` : ''}
        </div>
    `;

    // Set action button based on notification type
    const actionButton = document.getElementById('notification-action-btn');

    if (notification.type === 'debt') {
        actionButton.textContent = 'تسجيل دفعة';
        actionButton.className = 'btn btn-success';
        actionButton.style.display = 'block';
        actionButton.onclick = function() {
            window.location.href = `payment.html?student_id=${notification.student_id}`;
        };
    } else if (notification.type === 'installment') {
        actionButton.textContent = 'عرض خطة الأقساط';
        actionButton.className = 'btn btn-primary';
        actionButton.style.display = 'block';
        actionButton.onclick = function() {
            window.location.href = `installments.html?student_id=${notification.student_id}`;
        };
    } else {
        actionButton.style.display = 'none';
    }

    // Show modal
    try {
        const modalElement = document.getElementById('notificationDetailsModal');
        const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);
        modal.show();
    } catch (error) {
        console.error('Error showing modal:', error);
    }
}

// Mark all notifications as read
function markAllAsRead() {
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];

    // Mark all as read
    notifications.forEach(notification => {
        notification.read = true;
    });

    localStorage.setItem('notifications', JSON.stringify(notifications));

    // Reload notifications
    loadNotifications();

    alert('تم تعيين جميع الإشعارات كمقروءة');
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}
