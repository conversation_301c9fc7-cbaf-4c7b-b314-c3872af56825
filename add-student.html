<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة طالب جديد - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إضافة طالب جديد</h2>
                    <a href="students.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                    </a>
                </div>
                
                <!-- Display errors if any -->
                <div id="error-alert" class="alert alert-danger d-none">
                    <ul id="error-list" class="mb-0">
                    </ul>
                </div>
                
                <!-- Add student form -->
                <div class="card">
                    <div class="card-body">
                        <form id="add-student-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">اسم الطالب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="class" class="form-label">الصف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="class" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="student_id" class="form-label">رقم هوية الطالب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="student_id" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="parent_name" class="form-label">اسم ولي الأمر <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="parent_name" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone_number" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="phone_number" required>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/add-student.js"></script>
</body>
</html>
