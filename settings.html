<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="css/dark-mode.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.html">المعاملات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="installments.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> نظام الأقساط
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="users.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-users-cog me-2"></i> إدارة المستخدمين
                        </a>
                        <a href="settings.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إعدادات النظام</h2>
                    <div>
                        <button id="save-settings-btn" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
                
                <!-- Settings Tabs -->
                <div class="card">
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                                    <i class="fas fa-school me-1"></i> إعدادات عامة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="currency-tab" data-bs-toggle="tab" data-bs-target="#currency" type="button" role="tab" aria-controls="currency" aria-selected="false">
                                    <i class="fas fa-money-bill me-1"></i> إعدادات العملة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab" aria-controls="notifications" aria-selected="false">
                                    <i class="fas fa-bell me-1"></i> إعدادات الإشعارات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="false">
                                    <i class="fas fa-database me-1"></i> إعدادات النظام
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content p-3 border border-top-0 rounded-bottom" id="settingsTabsContent">
                            <!-- General Settings -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                                <form id="general-settings-form">
                                    <div class="mb-3">
                                        <label for="school-name" class="form-label">اسم المدرسة</label>
                                        <input type="text" class="form-control" id="school-name" placeholder="أدخل اسم المدرسة">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="school-logo" class="form-label">شعار المدرسة</label>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <img id="logo-preview" src="img/default-logo.png" alt="School Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                            </div>
                                            <div class="flex-grow-1">
                                                <input type="file" class="form-control" id="school-logo" accept="image/*">
                                                <div class="form-text">يفضل استخدام صورة بأبعاد 200×200 بكسل</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="school-address" class="form-label">عنوان المدرسة</label>
                                        <textarea class="form-control" id="school-address" rows="2" placeholder="أدخل عنوان المدرسة"></textarea>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="school-phone" class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="school-phone" placeholder="أدخل رقم الهاتف">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="school-email" class="form-label">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="school-email" placeholder="أدخل البريد الإلكتروني">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="academic-year" class="form-label">العام الدراسي الحالي</label>
                                        <input type="text" class="form-control" id="academic-year" placeholder="مثال: 2023-2024">
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Currency Settings -->
                            <div class="tab-pane fade" id="currency" role="tabpanel" aria-labelledby="currency-tab">
                                <form id="currency-settings-form">
                                    <div class="mb-3">
                                        <label for="currency-name" class="form-label">اسم العملة</label>
                                        <input type="text" class="form-control" id="currency-name" value="دينار كويتي">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="currency-code" class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currency-code" value="KWD">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="currency-symbol" class="form-label">رمز العملة المختصر</label>
                                        <input type="text" class="form-control" id="currency-symbol" value="د.ك">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="decimal-places" class="form-label">عدد الخانات العشرية</label>
                                        <input type="number" class="form-control" id="decimal-places" value="3" min="0" max="3">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="decimal-separator" class="form-label">فاصل الخانات العشرية</label>
                                        <select class="form-select" id="decimal-separator">
                                            <option value="." selected>نقطة (.)</option>
                                            <option value=",">فاصلة (,)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="thousands-separator" class="form-label">فاصل الآلاف</label>
                                        <select class="form-select" id="thousands-separator">
                                            <option value=",">فاصلة (,)</option>
                                            <option value=".">نقطة (.)</option>
                                            <option value=" ">مسافة ( )</option>
                                            <option value="">بدون فاصل</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="currency-position" class="form-label">موضع رمز العملة</label>
                                        <select class="form-select" id="currency-position">
                                            <option value="before">قبل المبلغ (د.ك 100)</option>
                                            <option value="after" selected>بعد المبلغ (100 د.ك)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="small-currency-name" class="form-label">اسم العملة الصغرى</label>
                                        <input type="text" class="form-control" id="small-currency-name" value="فلس">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="small-currency-ratio" class="form-label">نسبة العملة الصغرى للعملة الرئيسية</label>
                                        <input type="number" class="form-control" id="small-currency-ratio" value="1000">
                                        <div class="form-text">مثال: 1 دينار = 1000 فلس</div>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="show-small-currency" checked>
                                        <label class="form-check-label" for="show-small-currency">عرض المبالغ الصغيرة بالعملة الصغرى</label>
                                        <div class="form-text">مثال: 0.500 دينار سيتم عرضها كـ 500 فلس</div>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Notifications Settings -->
                            <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                                <form id="notifications-settings-form">
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="enable-notifications" checked>
                                        <label class="form-check-label" for="enable-notifications">تفعيل نظام الإشعارات</label>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="enable-sms-notifications">
                                        <label class="form-check-label" for="enable-sms-notifications">تفعيل الإشعارات عبر الرسائل النصية</label>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="enable-whatsapp-notifications" checked>
                                        <label class="form-check-label" for="enable-whatsapp-notifications">تفعيل الإشعارات عبر الواتساب</label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="default-country-code" class="form-label">رمز الدولة الافتراضي</label>
                                        <input type="text" class="form-control" id="default-country-code" value="+965">
                                    </div>
                                    
                                    <h5 class="mt-4 mb-3">إعدادات الإشعارات التلقائية</h5>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="auto-debt-notifications" checked>
                                        <label class="form-check-label" for="auto-debt-notifications">إرسال إشعارات تلقائية للديون المستحقة</label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="debt-notification-threshold" class="form-label">الحد الأدنى للدين لإرسال الإشعارات (بالدينار)</label>
                                        <input type="number" class="form-control" id="debt-notification-threshold" value="5.000" step="0.001">
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="auto-installment-notifications" checked>
                                        <label class="form-check-label" for="auto-installment-notifications">إرسال إشعارات تلقائية للأقساط المستحقة</label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="installment-notification-days" class="form-label">عدد أيام التذكير قبل استحقاق القسط</label>
                                        <input type="number" class="form-control" id="installment-notification-days" value="3" min="0">
                                    </div>
                                    
                                    <h5 class="mt-4 mb-3">قوالب الرسائل</h5>
                                    
                                    <div class="mb-3">
                                        <label for="debt-notification-template" class="form-label">قالب إشعار الدين</label>
                                        <textarea class="form-control" id="debt-notification-template" rows="3">عزيزي ولي أمر الطالب {student_name}، نود إعلامكم بأن هناك مبلغ {debt_amount} مستحق عليكم لصالح المقصف المدرسي. نرجو سداد المبلغ في أقرب وقت ممكن. مع الشكر.</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {student_name}، {student_id}، {class}، {debt_amount}</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="installment-notification-template" class="form-label">قالب إشعار القسط</label>
                                        <textarea class="form-control" id="installment-notification-template" rows="3">عزيزي ولي أمر الطالب {student_name}، نود تذكيركم بموعد استحقاق القسط رقم {installment_number} بمبلغ {installment_amount} بتاريخ {due_date}. مع الشكر.</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {student_name}، {student_id}، {class}، {installment_number}، {installment_amount}، {due_date}</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="payment-notification-template" class="form-label">قالب إشعار الدفع</label>
                                        <textarea class="form-control" id="payment-notification-template" rows="3">عزيزي ولي أمر الطالب {student_name}، نود إعلامكم بأنه تم استلام دفعة بمبلغ {payment_amount} بتاريخ {payment_date}. الرصيد المتبقي: {remaining_balance}. مع الشكر.</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {student_name}، {student_id}، {class}، {payment_amount}، {payment_date}، {remaining_balance}</div>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- System Settings -->
                            <div class="tab-pane fade" id="system" role="tabpanel" aria-labelledby="system-tab">
                                <form id="system-settings-form">
                                    <div class="mb-3">
                                        <label for="data-retention-period" class="form-label">فترة الاحتفاظ بالبيانات (بالأشهر)</label>
                                        <input type="number" class="form-control" id="data-retention-period" value="36" min="1">
                                        <div class="form-text">سيتم أرشفة البيانات الأقدم من هذه الفترة</div>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="enable-auto-backup" checked>
                                        <label class="form-check-label" for="enable-auto-backup">تفعيل النسخ الاحتياطي التلقائي</label>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="backup-frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                        <select class="form-select" id="backup-frequency">
                                            <option value="daily">يومي</option>
                                            <option value="weekly" selected>أسبوعي</option>
                                            <option value="monthly">شهري</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="backup-location" class="form-label">مسار حفظ النسخ الاحتياطي</label>
                                        <input type="text" class="form-control" id="backup-location" value="./backups">
                                    </div>
                                    
                                    <div class="d-grid gap-2 mt-4">
                                        <button type="button" id="backup-now-btn" class="btn btn-primary">
                                            <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية الآن
                                        </button>
                                        
                                        <button type="button" id="restore-backup-btn" class="btn btn-warning">
                                            <i class="fas fa-upload me-1"></i> استعادة من نسخة احتياطية
                                        </button>
                                        
                                        <input type="file" id="backup-file" class="d-none" accept=".json">
                                    </div>
                                    
                                    <hr class="my-4">
                                    
                                    <h5 class="text-danger mb-3">إعدادات متقدمة</h5>
                                    
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        تحذير: هذه الإعدادات متقدمة وقد تؤدي إلى فقدان البيانات. يرجى توخي الحذر.
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="button" id="clear-all-data-btn" class="btn btn-outline-danger">
                                            <i class="fas fa-trash-alt me-1"></i> حذف جميع البيانات
                                        </button>
                                        
                                        <button type="button" id="reset-settings-btn" class="btn btn-outline-secondary">
                                            <i class="fas fa-redo me-1"></i> إعادة تعيين الإعدادات الافتراضية
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>
    
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">تأكيد العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmation-message">
                    هل أنت متأكد من أنك تريد المتابعة؟
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirm-action-btn">تأكيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/dark-mode.js"></script>
</body>
</html>
