<?php
// Include authentication
require_once 'config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once 'config/db.php';

// Get total students count
$stmt = $pdo->query('SELECT COUNT(*) as total FROM students');
$totalStudents = $stmt->fetch()['total'] ?? 0;

// Get total debt amount
$stmt = $pdo->query('SELECT
    SUM(CASE WHEN type = "purchase" THEN amount ELSE 0 END) -
    SUM(CASE WHEN type = "payment" THEN amount ELSE 0 END) as total_debt
    FROM transactions');
$totalDebt = $stmt->fetch()['total_debt'] ?? 0;

// Get students with highest debt
$stmt = $pdo->query('SELECT s.id, s.name, s.class,
    SUM(CASE WHEN t.type = "purchase" THEN t.amount ELSE 0 END) -
    SUM(CASE WHEN t.type = "payment" THEN t.amount ELSE 0 END) as debt
    FROM students s
    LEFT JOIN transactions t ON s.id = t.student_id
    GROUP BY s.id
    HAVING debt > 0
    ORDER BY debt DESC
    LIMIT 5');
$highestDebtStudents = $stmt->fetchAll();

// Get recent transactions
$stmt = $pdo->query('SELECT t.id, t.type, t.amount, t.date, t.description, s.name as student_name
    FROM transactions t
    JOIN students s ON t.student_id = s.id
    ORDER BY t.date DESC
    LIMIT 10');
$recentTransactions = $stmt->fetchAll();

// Include header
include 'includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main content -->
    <div class="col-md-9">
        <h2 class="mb-4">لوحة التحكم</h2>

        <!-- Dashboard cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card dashboard-card bg-primary text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h5 class="card-title">إجمالي الطلاب</h5>
                        <h2 class="mb-0"><?php echo $totalStudents; ?></h2>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card dashboard-card bg-danger text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <h5 class="card-title">إجمالي الديون</h5>
                        <h2 class="mb-0"><?php echo number_format($totalDebt, 2); ?> دينار</h2>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <h5 class="card-title">التاريخ الحالي</h5>
                        <h2 class="mb-0"><?php echo date('d/m/Y'); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Students with highest debt -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-exclamation-triangle me-2"></i> الطلاب الأكثر مديونية
                    </div>
                    <div class="card-body">
                        <?php if (count($highestDebtStudents) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الصف</th>
                                            <th>المبلغ</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($highestDebtStudents as $student): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['class']); ?></td>
                                                <td class="text-danger fw-bold"><?php echo number_format($student['debt'], 2); ?> ريال</td>
                                                <td>
                                                    <a href="students/view.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">لا يوجد طلاب مدينين حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent transactions -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-history me-2"></i> آخر المعاملات
                    </div>
                    <div class="card-body">
                        <?php if (count($recentTransactions) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentTransactions as $transaction): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($transaction['student_name']); ?></td>
                                                <td>
                                                    <?php if ($transaction['type'] === 'purchase'): ?>
                                                        <span class="badge bg-danger">مشتريات</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">دفعة</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($transaction['type'] === 'purchase'): ?>
                                                        <span class="text-danger"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                                    <?php else: ?>
                                                        <span class="text-success"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($transaction['date'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">لا توجد معاملات حديثة</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick actions -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-bolt me-2"></i> إجراءات سريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="students/add.php" class="btn btn-outline-primary w-100 p-3">
                            <i class="fas fa-user-plus mb-2 d-block" style="font-size: 2rem;"></i>
                            إضافة طالب جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="transactions/new.php" class="btn btn-outline-danger w-100 p-3">
                            <i class="fas fa-shopping-cart mb-2 d-block" style="font-size: 2rem;"></i>
                            تسجيل مشتريات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="transactions/payment.php" class="btn btn-outline-success w-100 p-3">
                            <i class="fas fa-hand-holding-usd mb-2 d-block" style="font-size: 2rem;"></i>
                            تسجيل دفعة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="reports/generate.php" class="btn btn-outline-info w-100 p-3">
                            <i class="fas fa-file-alt mb-2 d-block" style="font-size: 2rem;"></i>
                            إنشاء تقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
