<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الأقساط - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="installments.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-calendar-alt me-2"></i> نظام الأقساط
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice me-2"></i> الفواتير وسندات القبض
                        </a>
                        <a href="import-export.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-excel me-2"></i> استيراد/تصدير البيانات
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>نظام الأقساط</h2>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInstallmentPlanModal">
                        <i class="fas fa-plus-circle me-1"></i> إنشاء خطة أقساط جديدة
                    </button>
                </div>
                
                <!-- Tabs -->
                <ul class="nav nav-tabs mb-4" id="installmentsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-plans-tab" data-bs-toggle="tab" data-bs-target="#active-plans" type="button" role="tab" aria-controls="active-plans" aria-selected="true">
                            <i class="fas fa-check-circle me-1"></i> خطط الأقساط النشطة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="upcoming-payments-tab" data-bs-toggle="tab" data-bs-target="#upcoming-payments" type="button" role="tab" aria-controls="upcoming-payments" aria-selected="false">
                            <i class="fas fa-calendar-day me-1"></i> الدفعات القادمة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="completed-plans-tab" data-bs-toggle="tab" data-bs-target="#completed-plans" type="button" role="tab" aria-controls="completed-plans" aria-selected="false">
                            <i class="fas fa-clipboard-check me-1"></i> خطط الأقساط المكتملة
                        </button>
                    </li>
                </ul>
                
                <!-- Tab content -->
                <div class="tab-content" id="installmentsTabsContent">
                    <!-- Active Plans Tab -->
                    <div class="tab-pane fade show active" id="active-plans" role="tabpanel" aria-labelledby="active-plans-tab">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>عدد الأقساط</th>
                                                <th>المدفوع</th>
                                                <th>المتبقي</th>
                                                <th>تاريخ القسط القادم</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="active-plans-table">
                                            <!-- Will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Upcoming Payments Tab -->
                    <div class="tab-pane fade" id="upcoming-payments" role="tabpanel" aria-labelledby="upcoming-payments-tab">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>رقم القسط</th>
                                                <th>المبلغ</th>
                                                <th>تاريخ الاستحقاق</th>
                                                <th>الأيام المتبقية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="upcoming-payments-table">
                                            <!-- Will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Completed Plans Tab -->
                    <div class="tab-pane fade" id="completed-plans" role="tabpanel" aria-labelledby="completed-plans-tab">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>الطالب</th>
                                                <th>إجمالي المبلغ</th>
                                                <th>عدد الأقساط</th>
                                                <th>تاريخ البدء</th>
                                                <th>تاريخ الانتهاء</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="completed-plans-table">
                                            <!-- Will be populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create Installment Plan Modal -->
    <div class="modal fade" id="createInstallmentPlanModal" tabindex="-1" aria-labelledby="createInstallmentPlanModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createInstallmentPlanModalLabel">إنشاء خطة أقساط جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="installment-plan-form">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="student-select" class="form-label">الطالب <span class="text-danger">*</span></label>
                                <select class="form-select" id="student-select" required>
                                    <option value="">اختر الطالب...</option>
                                    <!-- Will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="current-debt" class="form-label">الدين الحالي</label>
                                <input type="text" class="form-control" id="current-debt" readonly>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="installment-amount" class="form-label">المبلغ المراد تقسيطه <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="installment-amount" min="1" step="0.001" required>
                                    <span class="input-group-text">د.ك</span>
                                </div>
                                <div class="form-text">يمكن تقسيط جزء من الدين أو كامل المبلغ</div>
                            </div>
                            <div class="col-md-6">
                                <label for="installment-count" class="form-label">عدد الأقساط <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="installment-count" min="2" max="12" value="3" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="first-payment-date" class="form-label">تاريخ أول قسط <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="first-payment-date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="payment-frequency" class="form-label">تكرار الدفع <span class="text-danger">*</span></label>
                                <select class="form-select" id="payment-frequency" required>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="biweekly">كل أسبوعين</option>
                                    <option value="monthly" selected>شهري</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="installment-notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="installment-notes" rows="2"></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <div class="d-flex">
                                <div class="me-2">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="alert-heading">معلومات الأقساط</h6>
                                    <p class="mb-0">قيمة القسط: <span id="installment-value">0.000</span> د.ك</p>
                                    <p class="mb-0">تاريخ آخر قسط: <span id="last-payment-date">-</span></p>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="create-installment-plan-btn">إنشاء خطة الأقساط</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- View Installment Plan Modal -->
    <div class="modal fade" id="viewInstallmentPlanModal" tabindex="-1" aria-labelledby="viewInstallmentPlanModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewInstallmentPlanModalLabel">تفاصيل خطة الأقساط</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="installment-plan-details">
                    <!-- Will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="print-installment-plan-btn">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Pay Installment Modal -->
    <div class="modal fade" id="payInstallmentModal" tabindex="-1" aria-labelledby="payInstallmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="payInstallmentModalLabel">تسجيل دفع قسط</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="pay-installment-form">
                        <input type="hidden" id="payment-plan-id">
                        <input type="hidden" id="payment-installment-index">
                        
                        <div class="mb-3">
                            <label for="payment-student-name" class="form-label">الطالب</label>
                            <input type="text" class="form-control" id="payment-student-name" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment-amount" class="form-label">المبلغ</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="payment-amount" readonly>
                                <span class="input-group-text">د.ك</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment-date" class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="payment-date" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment-method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment-method" required>
                                <option value="cash">نقداً</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment-notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="payment-notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="confirm-payment-btn">تأكيد الدفع</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js for date calculations -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <!-- Hijri Date Library -->
    <script src="https://cdn.jsdelivr.net/npm/moment-hijri@2.1.2/moment-hijri.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/installments.js"></script>
</body>
</html>
