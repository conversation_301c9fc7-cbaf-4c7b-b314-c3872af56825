/* Dark Mode Styles */
:root {
    --dark-bg: #121212;
    --dark-card-bg: #1e1e1e;
    --dark-text: #e0e0e0;
    --dark-text-secondary: #aaaaaa;
    --dark-border: #333333;
    --dark-primary: #3f51b5;
    --dark-primary-hover: #303f9f;
    --dark-success: #43a047;
    --dark-danger: #e53935;
    --dark-warning: #ffb300;
    --dark-info: #039be5;
    --dark-table-header: #2c2c2c;
    --dark-table-row-hover: #2a2a2a;
    --dark-input-bg: #2d2d2d;
    --dark-input-border: #444444;
}

body.dark-mode {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

/* Navbar */
body.dark-mode .navbar-dark {
    background-color: var(--dark-primary) !important;
}

body.dark-mode .navbar-dark .navbar-brand,
body.dark-mode .navbar-dark .nav-link {
    color: var(--dark-text);
}

/* Cards */
body.dark-mode .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: var(--dark-border);
}

body.dark-mode .card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top-color: var(--dark-border);
}

/* Tables */
body.dark-mode .table {
    color: var(--dark-text);
}

body.dark-mode .table-light,
body.dark-mode .table-light > th,
body.dark-mode .table-light > td {
    background-color: var(--dark-table-header);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: var(--dark-table-row-hover);
    color: var(--dark-text);
}

body.dark-mode .table td,
body.dark-mode .table th {
    border-top-color: var(--dark-border);
}

body.dark-mode .table thead th {
    border-bottom-color: var(--dark-border);
}

/* Forms */
body.dark-mode .form-control {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-input-border);
    color: var(--dark-text);
}

body.dark-mode .form-control:focus {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-primary);
    color: var(--dark-text);
}

body.dark-mode .form-select {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-input-border);
    color: var(--dark-text);
}

body.dark-mode .input-group-text {
    background-color: var(--dark-table-header);
    border-color: var(--dark-input-border);
    color: var(--dark-text);
}

/* Buttons */
body.dark-mode .btn-primary {
    background-color: var(--dark-primary);
    border-color: var(--dark-primary);
}

body.dark-mode .btn-primary:hover {
    background-color: var(--dark-primary-hover);
    border-color: var(--dark-primary-hover);
}

body.dark-mode .btn-outline-primary {
    color: var(--dark-primary);
    border-color: var(--dark-primary);
}

body.dark-mode .btn-outline-primary:hover {
    background-color: var(--dark-primary);
    color: var(--dark-text);
}

body.dark-mode .btn-secondary {
    background-color: #616161;
    border-color: #616161;
}

body.dark-mode .btn-secondary:hover {
    background-color: #4e4e4e;
    border-color: #4e4e4e;
}

body.dark-mode .btn-success {
    background-color: var(--dark-success);
    border-color: var(--dark-success);
}

body.dark-mode .btn-danger {
    background-color: var(--dark-danger);
    border-color: var(--dark-danger);
}

body.dark-mode .btn-warning {
    background-color: var(--dark-warning);
    border-color: var(--dark-warning);
}

body.dark-mode .btn-info {
    background-color: var(--dark-info);
    border-color: var(--dark-info);
}

/* List Group */
body.dark-mode .list-group-item {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .list-group-item.active {
    background-color: var(--dark-primary);
    border-color: var(--dark-primary);
}

body.dark-mode .list-group-item-action:hover {
    background-color: var(--dark-table-row-hover);
    color: var(--dark-text);
}

/* Modals */
body.dark-mode .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .modal-header {
    border-bottom-color: var(--dark-border);
}

body.dark-mode .modal-footer {
    border-top-color: var(--dark-border);
}

/* Alerts */
body.dark-mode .alert-info {
    background-color: rgba(3, 155, 229, 0.2);
    border-color: rgba(3, 155, 229, 0.3);
    color: #81d4fa;
}

body.dark-mode .alert-success {
    background-color: rgba(67, 160, 71, 0.2);
    border-color: rgba(67, 160, 71, 0.3);
    color: #a5d6a7;
}

body.dark-mode .alert-warning {
    background-color: rgba(255, 179, 0, 0.2);
    border-color: rgba(255, 179, 0, 0.3);
    color: #ffe082;
}

body.dark-mode .alert-danger {
    background-color: rgba(229, 57, 53, 0.2);
    border-color: rgba(229, 57, 53, 0.3);
    color: #ef9a9a;
}

/* Badges */
body.dark-mode .badge.bg-primary {
    background-color: var(--dark-primary) !important;
}

body.dark-mode .badge.bg-success {
    background-color: var(--dark-success) !important;
}

body.dark-mode .badge.bg-danger {
    background-color: var(--dark-danger) !important;
}

body.dark-mode .badge.bg-warning {
    background-color: var(--dark-warning) !important;
}

body.dark-mode .badge.bg-info {
    background-color: var(--dark-info) !important;
}

/* Progress Bars */
body.dark-mode .progress {
    background-color: var(--dark-input-bg);
}

/* Tooltips */
body.dark-mode .tooltip-inner {
    background-color: var(--dark-card-bg);
    color: var(--dark-text);
}

/* Footer */
body.dark-mode footer.bg-light {
    background-color: var(--dark-card-bg) !important;
}

body.dark-mode footer .text-muted {
    color: var(--dark-text-secondary) !important;
}

/* Text Colors */
body.dark-mode .text-muted {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .text-dark {
    color: var(--dark-text) !important;
}

/* Charts */
body.dark-mode canvas {
    filter: brightness(0.8) contrast(1.2);
}

/* Dark Mode Toggle Button */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #333;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
}

body.dark-mode .dark-mode-toggle {
    background-color: #f8f9fa;
    color: #333;
}

/* Print Styles */
@media print {
    body.dark-mode {
        background-color: white !important;
        color: black !important;
    }
    
    body.dark-mode * {
        background-color: white !important;
        color: black !important;
        border-color: #ddd !important;
    }
    
    body.dark-mode .dark-mode-toggle {
        display: none !important;
    }
}
