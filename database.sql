-- Create database
CREATE DATABASE IF NOT EXISTS canteen_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE canteen_management;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'cashier') NOT NULL DEFAULT 'cashier',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    class VARCHAR(50) NOT NULL,
    student_id VARCHAR(50) NOT NULL UNIQUE,
    parent_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    type ENUM('purchase', 'payment') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2y$10$8tGmGEOg1LLgSgJwQYXlleWVjxjSUvHfJN1zIlHEYvGhsxJQZgX7.', 'admin');

-- Insert sample data (optional)
INSERT INTO students (name, class, student_id, parent_name, phone_number) VALUES
('أحمد محمد', 'الصف الثالث', '1001', 'محمد أحمد', '0501234567'),
('سارة خالد', 'الصف الثاني', '1002', 'خالد سعيد', '0509876543'),
('عمر علي', 'الصف الأول', '1003', 'علي عمر', '0507654321'),
('فاطمة أحمد', 'الصف الثالث', '1004', 'أحمد محمود', '0501122334'),
('محمد سعيد', 'الصف الثاني', '1005', 'سعيد محمد', '0505544332');

-- Insert sample transactions
INSERT INTO transactions (student_id, type, amount, description, date, created_by) VALUES
(1, 'purchase', 15.00, 'وجبة غداء', CURDATE(), 1),
(1, 'purchase', 5.00, 'عصير', CURDATE(), 1),
(2, 'purchase', 20.00, 'وجبة غداء وعصير', CURDATE(), 1),
(3, 'purchase', 10.00, 'سندويش', CURDATE(), 1),
(4, 'purchase', 25.00, 'وجبة كاملة', CURDATE(), 1),
(5, 'purchase', 15.00, 'وجبة غداء', CURDATE(), 1),
(2, 'payment', 10.00, 'دفعة جزئية', CURDATE(), 1),
(4, 'payment', 25.00, 'دفعة كاملة', CURDATE(), 1);
