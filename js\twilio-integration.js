// Twilio WhatsApp Integration

// Define the Twilio integration object
const TwilioIntegration = {
    // Configuration
    config: {
        accountSid: 'YOUR_ACCOUNT_SID',
        authToken: 'YOUR_AUTH_TOKEN',
        whatsappNumber: 'whatsapp:+***********',
        apiUrl: 'https://api.twilio.com/2010-04-01/Accounts/'
    },

    // Initialize
    init: function() {
        // Load config from localStorage
        const storedConfig = localStorage.getItem('twilioConfig');
        if (storedConfig) {
            try {
                const parsedConfig = JSON.parse(storedConfig);
                this.config = { ...this.config, ...parsedConfig };
            } catch (e) {
                console.error('Error parsing Twilio config:', e);
            }
        }
        console.log('Twilio integration initialized');
    },

    // Save configuration
    saveConfig: function(newConfig) {
        this.config = { ...this.config, ...newConfig };
        localStorage.setItem('twilioConfig', JSON.stringify(this.config));
        console.log('Twilio configuration saved');
        return true;
    },

    // Test connection
    testConnection: async function() {
        console.log('Testing Twilio connection...');

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check if credentials are set
        if (!this.config.accountSid || this.config.accountSid === 'YOUR_ACCOUNT_SID' ||
            !this.config.authToken || this.config.authToken === 'YOUR_AUTH_TOKEN') {
            return {
                success: false,
                message: 'Twilio credentials are not configured.'
            };
        }

        // Simulate successful response
        return {
            success: true,
            message: 'Twilio connection successful!',
            account: {
                sid: this.config.accountSid,
                friendlyName: 'School Canteen System',
                status: 'active'
            }
        };
    },

    // Send WhatsApp message
    sendMessage: async function(to, message) {
        // Check if Twilio is configured
        if (!this.config.accountSid || this.config.accountSid === 'YOUR_ACCOUNT_SID') {
            throw new Error('Twilio is not configured. Please configure Twilio in the WhatsApp settings.');
        }

        // Format the recipient's phone number for WhatsApp
        to = to.replace(/\D/g, '');
        if (!to.startsWith('+')) {
            to = '+' + to;
        }

        // Format as WhatsApp number
        const formattedTo = 'whatsapp:' + to;

        console.log(`Sending WhatsApp message to ${formattedTo}: ${message}`);

        try {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Simulate successful response
            const response = {
                sid: 'SM' + Math.random().toString(36).substr(2, 9),
                status: 'queued',
                to: formattedTo,
                from: this.config.whatsappNumber,
                body: message,
                date_created: new Date().toISOString()
            };

            // Log the message to localStorage
            this.logMessage(to, message);

            return response;
        } catch (error) {
            console.error('Error sending WhatsApp message:', error);
            throw error;
        }
    },

    // Log sent message
    logMessage: function(to, message) {
        const sentMessages = this.getSentMessages();
        sentMessages.push({
            to: to,
            message: message,
            timestamp: new Date().toISOString(),
            status: 'sent'
        });
        localStorage.setItem('sentWhatsAppMessages', JSON.stringify(sentMessages));
    },

    // Get sent messages
    getSentMessages: function() {
        return JSON.parse(localStorage.getItem('sentWhatsAppMessages') || '[]');
    },

    // Clear sent messages
    clearSentMessages: function() {
        localStorage.removeItem('sentWhatsAppMessages');
        return true;
    }
};

// Initialize Twilio integration
TwilioIntegration.init();

// Export to window object
window.TwilioIntegration = TwilioIntegration;
