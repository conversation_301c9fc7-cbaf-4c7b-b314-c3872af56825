<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الواتساب - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="css/dark-mode.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transactions.html">المعاملات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="system-settings.html"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="installments.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> نظام الأقساط
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="whatsapp-settings.html" class="list-group-item list-group-item-action active">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            قم بإعداد الواتساب لإرسال الإشعارات والرسائل لأولياء الأمور.
                        </div>

                        <!-- Twilio Integration Settings -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-plug me-2"></i> إعدادات Twilio
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    لإرسال رسائل واتساب باستخدام Twilio، يجب إدخال بيانات الاعتماد الخاصة بك من حساب Twilio.
                                </div>

                                <form id="twilio-settings-form">
                                    <div class="mb-3">
                                        <label for="twilio-account-sid" class="form-label">Account SID</label>
                                        <input type="text" class="form-control" id="twilio-account-sid" placeholder="أدخل Account SID الخاص بك">
                                        <div class="form-text">يمكنك العثور على هذا في لوحة تحكم Twilio الخاصة بك</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="twilio-auth-token" class="form-label">Auth Token</label>
                                        <input type="password" class="form-control" id="twilio-auth-token" placeholder="أدخل Auth Token الخاص بك">
                                        <div class="form-text">يمكنك العثور على هذا في لوحة تحكم Twilio الخاصة بك</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="twilio-whatsapp-number" class="form-label">رقم واتساب Twilio</label>
                                        <input type="text" class="form-control" id="twilio-whatsapp-number" placeholder="مثال: whatsapp:+14155238886">
                                        <div class="form-text">أدخل رقم واتساب Twilio الخاص بك مسبوقًا بـ 'whatsapp:'</div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="button" id="test-twilio-connection" class="btn btn-info me-md-2">
                                            <i class="fas fa-vial me-1"></i> اختبار الاتصال
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> حفظ الإعدادات
                                        </button>
                                    </div>
                                </form>

                                <div id="twilio-connection-status" class="mt-4" style="display: none;">
                                    <!-- Connection status will be displayed here -->
                                </div>
                            </div>
                        </div>

                        <!-- WhatsApp Connection Status -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i> سجل الرسائل المرسلة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>المستلم</th>
                                                <th>الرسالة</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody id="sent-messages-table">
                                            <!-- Will be populated by JavaScript -->
                                            <tr>
                                                <td colspan="4" class="text-center">لم يتم إرسال أي رسائل بعد</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                                    <button type="button" id="refresh-messages" class="btn btn-light">
                                        <i class="fas fa-sync-alt me-1"></i> تحديث السجل
                                    </button>
                                    <button type="button" id="clear-messages" class="btn btn-outline-danger">
                                        <i class="fas fa-trash-alt me-1"></i> مسح السجل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- WhatsApp Settings -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i> إعدادات الرسائل
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="whatsapp-settings-form">
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="enable-whatsapp" checked>
                                        <label class="form-check-label" for="enable-whatsapp">تفعيل إرسال رسائل الواتساب</label>
                                    </div>

                                    <div class="mb-3">
                                        <label for="default-country-code" class="form-label">رمز الدولة الافتراضي</label>
                                        <input type="text" class="form-control" id="default-country-code" value="+965">
                                        <div class="form-text">سيتم إضافة هذا الرمز تلقائيًا إذا لم يكن موجودًا في رقم الهاتف</div>
                                    </div>

                                    <h6 class="mt-4 mb-3">قوالب الرسائل</h6>

                                    <div class="mb-3">
                                        <label for="debt-notification-template" class="form-label">قالب إشعار الدين</label>
                                        <textarea class="form-control" id="debt-notification-template" rows="4">مرحباً {parent_name}،
نود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.
نرجو سداد المبلغ في أقرب وقت ممكن.
مع الشكر، إدارة المدرسة</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {parent_name}، {student_name}، {student_class}، {debt_amount}، {school_name}</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="payment-confirmation-template" class="form-label">قالب تأكيد الدفع</label>
                                        <textarea class="form-control" id="payment-confirmation-template" rows="4">مرحباً {parent_name}،
نشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.
الرصيد المتبقي: {remaining_debt}
مع الشكر، إدارة المدرسة</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {parent_name}، {student_name}، {payment_amount}، {payment_date}، {remaining_debt}</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="custom-message-template" class="form-label">قالب الرسالة المخصصة</label>
                                        <textarea class="form-control" id="custom-message-template" rows="4">مرحباً {parent_name}،
{message}
مع الشكر، إدارة المدرسة</textarea>
                                        <div class="form-text">يمكنك استخدام الرموز التالية: {parent_name}، {student_name}، {message}، {school_name}</div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Send Test Message -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-paper-plane me-2"></i> إرسال رسالة تجريبية
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="test-message-form">
                                    <div class="mb-3">
                                        <label for="test-phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text" id="test-country-code">+965</span>
                                            <input type="text" class="form-control" id="test-phone" placeholder="أدخل رقم الهاتف" required>
                                        </div>
                                        <div class="form-text">أدخل رقم الهاتف بدون رمز الدولة</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="test-message-type" class="form-label">نوع الرسالة <span class="text-danger">*</span></label>
                                        <select class="form-select" id="test-message-type" required>
                                            <option value="">اختر نوع الرسالة...</option>
                                            <option value="debt">إشعار دين</option>
                                            <option value="payment">تأكيد دفع</option>
                                            <option value="custom">رسالة مخصصة</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="test-custom-message-container" style="display: none;">
                                        <label for="test-custom-message" class="form-label">الرسالة المخصصة <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="test-custom-message" rows="4" placeholder="أدخل نص الرسالة المخصصة"></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fab fa-whatsapp me-1"></i> إرسال رسالة تجريبية
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Bulk Message -->
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i> إرسال رسائل جماعية
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="bulk-message-form">
                                    <div class="mb-3">
                                        <label for="bulk-recipients" class="form-label">المستلمون <span class="text-danger">*</span></label>
                                        <select class="form-select" id="bulk-recipients" required>
                                            <option value="">اختر المستلمين...</option>
                                            <option value="all">جميع أولياء الأمور</option>
                                            <option value="with_debt">أولياء أمور الطلاب المدينين فقط</option>
                                            <option value="class">حسب الصف الدراسي</option>
                                            <option value="custom">اختيار مخصص</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="bulk-class-container" style="display: none;">
                                        <label for="bulk-class" class="form-label">الصف الدراسي <span class="text-danger">*</span></label>
                                        <select class="form-select" id="bulk-class">
                                            <option value="">اختر الصف الدراسي...</option>
                                            <!-- Will be populated by JavaScript -->
                                        </select>
                                    </div>

                                    <div class="mb-3" id="bulk-custom-container" style="display: none;">
                                        <label for="bulk-custom-students" class="form-label">اختر الطلاب <span class="text-danger">*</span></label>
                                        <select class="form-select" id="bulk-custom-students" multiple size="8">
                                            <!-- Will be populated by JavaScript -->
                                        </select>
                                        <div class="form-text">اضغط Ctrl لاختيار عدة طلاب</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="bulk-message-type" class="form-label">نوع الرسالة <span class="text-danger">*</span></label>
                                        <select class="form-select" id="bulk-message-type" required>
                                            <option value="">اختر نوع الرسالة...</option>
                                            <option value="debt">إشعار دين</option>
                                            <option value="custom">رسالة مخصصة</option>
                                        </select>
                                    </div>

                                    <div class="mb-3" id="bulk-custom-message-container" style="display: none;">
                                        <label for="bulk-custom-message" class="form-label">الرسالة المخصصة <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="bulk-custom-message" rows="4" placeholder="أدخل نص الرسالة المخصصة"></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fab fa-whatsapp me-1"></i> إرسال الرسائل
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Preview Modal -->
    <div class="modal fade" id="messagePreviewModal" tabindex="-1" aria-labelledby="messagePreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messagePreviewModalLabel">معاينة الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">المستلمون:</label>
                        <div id="preview-recipients" class="border rounded p-2 bg-light"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نص الرسالة:</label>
                        <div id="preview-message" class="border rounded p-3 bg-light whatsapp-preview"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" id="confirm-send-btn">
                        <i class="fab fa-whatsapp me-1"></i> تأكيد الإرسال
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Sending Progress Modal -->
    <div class="modal fade" id="sendingProgressModal" tabindex="-1" aria-labelledby="sendingProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sendingProgressModalLabel">جاري إرسال الرسائل</h5>
                </div>
                <div class="modal-body text-center">
                    <div class="progress mb-3">
                        <div id="sending-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <p id="sending-status">جاري الإرسال... <span id="sending-count">0</span> من <span id="total-count">0</span></p>
                    <div id="sending-result" class="mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="close-progress-btn" style="display: none;">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/twilio-integration.js"></script>
    <script src="js/whatsapp-settings.js"></script>
    <script src="js/dark-mode.js"></script>
</body>
</html>
