// Dashboard functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

// Chart colors
const chartColors = [
    'rgba(54, 162, 235, 0.7)',   // Blue
    'rgba(255, 99, 132, 0.7)',    // Red
    'rgba(75, 192, 192, 0.7)',    // Green
    'rgba(255, 159, 64, 0.7)',    // Orange
    'rgba(153, 102, 255, 0.7)',   // Purple
    'rgba(255, 205, 86, 0.7)',    // Yellow
    'rgba(201, 203, 207, 0.7)',   // Grey
    'rgba(255, 99, 71, 0.7)',     // Tomato
    'rgba(50, 205, 50, 0.7)',     // Lime Green
    'rgba(138, 43, 226, 0.7)'     // Blue Violet
];

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set current date (Gregorian and Hijri)
    const now = new Date();
    const formattedDate = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
    document.getElementById('current-date').textContent = formattedDate;

    // Set Hijri date if moment-hijri is available
    if (typeof moment !== 'undefined' && typeof moment.locale === 'function') {
        moment.locale('ar-SA');
        const hijriDate = moment().format('iD/iM/iYYYY');
        document.getElementById('current-date-hijri').textContent = hijriDate + ' هـ';
    }

    // Load dashboard data
    loadDashboardData();

    // Initialize charts
    initializeCharts();

    // Update unread notifications count
    updateUnreadNotificationsCount();

    // Setup print functionality
    setupPrintFunctionality();
});

// Load dashboard data
function loadDashboardData() {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const expenses = JSON.parse(localStorage.getItem('expenses')) || [];

    // Update total students
    document.getElementById('total-students').textContent = students.length;

    // Calculate total debt and payments
    let totalDebt = 0;
    let totalPayments = 0;
    let totalPurchases = 0;
    const studentDebts = {};

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            id: student.id,
            name: student.name,
            class: student.class,
            total_purchases: 0,
            total_payments: 0,
            debt: 0
        };
    });

    // Get today's date for today's transactions
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Variables for today's transactions
    let todayTransactionsCount = 0;
    let todayTransactionsAmount = 0;

    // Calculate debts from transactions
    transactions.forEach(transaction => {
        const transactionAmount = parseFloat(transaction.amount);
        const transactionDate = new Date(transaction.date);

        // Check if transaction is from today
        if (transactionDate.setHours(0, 0, 0, 0) === today.getTime()) {
            todayTransactionsCount++;
            todayTransactionsAmount += transactionAmount;
        }

        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].total_purchases += transactionAmount;
            studentDebts[transaction.student_id].debt += transactionAmount;
            totalPurchases += transactionAmount;
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].total_payments += transactionAmount;
            studentDebts[transaction.student_id].debt -= transactionAmount;
            totalPayments += transactionAmount;
        }
    });

    // Calculate total debt
    totalDebt = totalPurchases - totalPayments;

    // Update total debt display
    document.getElementById('total-debt').textContent = formatKuwaitiCurrency(totalDebt);

    // Update total payments display
    document.getElementById('total-payments').textContent = formatKuwaitiCurrency(totalPayments);

    // Update today's transactions
    document.getElementById('today-transactions').textContent = todayTransactionsCount;
    document.getElementById('today-transactions-amount').textContent = formatKuwaitiCurrency(todayTransactionsAmount);

    // Count students with debt and without debt
    const studentsWithDebt = Object.values(studentDebts).filter(student => student.debt > 0).length;
    const studentsWithoutDebt = Object.values(studentDebts).filter(student => student.debt <= 0).length;

    // Update debtors count and percentage
    document.getElementById('debtors-count').textContent = studentsWithDebt;
    document.getElementById('debtors-percentage').textContent =
        students.length > 0 ? Math.round((studentsWithDebt / students.length) * 100) + '%' : '0%';

    // Update no-debt count and percentage
    document.getElementById('no-debt-count').textContent = studentsWithoutDebt;
    document.getElementById('no-debt-percentage').textContent =
        students.length > 0 ? Math.round((studentsWithoutDebt / students.length) * 100) + '%' : '0%';

    // Convert student debts to array and sort by debt amount
    const studentDebtsArray = Object.values(studentDebts)
        .filter(student => student.debt > 0)
        .sort((a, b) => b.debt - a.debt);

    // Display highest debt students (top 5)
    const highestDebtStudentsElement = document.getElementById('highest-debt-students');
    highestDebtStudentsElement.innerHTML = '';

    if (studentDebtsArray.length > 0) {
        studentDebtsArray.slice(0, 5).forEach(student => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${student.name}</td>
                <td>${student.class}</td>
                <td class="text-danger fw-bold">${formatKuwaitiCurrency(student.debt)}</td>
                <td>
                    <a href="student-debt-management.html?id=${student.id}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i>
                    </a>
                </td>
            `;
            highestDebtStudentsElement.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" class="text-center">لا يوجد طلاب مدينين حالياً</td>';
        highestDebtStudentsElement.appendChild(row);
    }

    // Display recent transactions (last 5)
    const recentTransactionsElement = document.getElementById('recent-transactions');
    recentTransactionsElement.innerHTML = '';

    if (transactions.length > 0) {
        // Sort transactions by date (newest first)
        const sortedTransactions = [...transactions].sort((a, b) => {
            return new Date(b.date) - new Date(a.date);
        });

        sortedTransactions.slice(0, 5).forEach(transaction => {
            const student = students.find(s => s.id === transaction.student_id);
            const studentName = student ? student.name : 'غير معروف';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${studentName}</td>
                <td>
                    ${transaction.type === 'purchase'
                        ? '<span class="badge bg-danger">مشتريات</span>'
                        : '<span class="badge bg-success">دفعة</span>'}
                </td>
                <td>
                    ${transaction.type === 'purchase'
                        ? `<span class="text-danger">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`
                        : `<span class="text-success">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`}
                </td>
                <td>${formatDate(transaction.date)}</td>
            `;
            recentTransactionsElement.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" class="text-center">لا توجد معاملات حديثة</td>';
        recentTransactionsElement.appendChild(row);
    }

    // Calculate expenses data
    let totalExpenses = 0;
    let monthExpenses = 0;
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    expenses.forEach(expense => {
        const expenseAmount = parseFloat(expense.amount);
        totalExpenses += expenseAmount;

        // Check if expense is in current month
        const expenseDate = new Date(expense.date);
        if (expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear) {
            monthExpenses += expenseAmount;
        }
    });

    // Update expenses displays
    document.getElementById('total-expenses').textContent = formatKuwaitiCurrency(totalExpenses);
    document.getElementById('month-expenses').textContent = formatKuwaitiCurrency(monthExpenses);

    // Calculate net profit (total payments - total expenses)
    const netProfit = totalPayments - totalExpenses;
    document.getElementById('net-profit').textContent = formatKuwaitiCurrency(netProfit);

    // Update data for charts
    window.dashboardData = {
        studentDebts: studentDebts,
        transactions: transactions,
        students: students,
        expenses: expenses
    };
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

// Initialize charts
function initializeCharts() {
    // Wait for dashboard data to be available
    if (!window.dashboardData) {
        setTimeout(initializeCharts, 100);
        return;
    }

    // Initialize debt distribution chart
    initializeDebtDistributionChart();

    // Initialize monthly transactions chart
    initializeMonthlyTransactionsChart();
}

// Initialize debt distribution chart
function initializeDebtDistributionChart() {
    const { studentDebts, students } = window.dashboardData;

    // Calculate debt by class
    const classTotals = {};

    // Calculate total debt by class
    Object.values(studentDebts).forEach(student => {
        if (student.debt > 0) {
            if (!classTotals[student.class]) {
                classTotals[student.class] = 0;
            }
            classTotals[student.class] += student.debt;
        }
    });

    // Prepare data for chart
    const labels = Object.keys(classTotals);
    const data = labels.map(className => classTotals[className]);

    // Get canvas element
    const canvas = document.getElementById('debtDistributionChart');

    // Create chart
    if (canvas) {
        // Clear existing chart if any
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Create new chart
        canvas.chart = new Chart(canvas, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    label: 'إجمالي الديون',
                    data: data,
                    backgroundColor: chartColors.slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${formatKuwaitiCurrency(value)}`;
                            }
                        }
                    }
                }
            }
        });
    }
}

// Update unread notifications count
function updateUnreadNotificationsCount() {
    const notifications = JSON.parse(localStorage.getItem('notifications')) || [];
    const unreadCount = notifications.filter(notification => !notification.read).length;

    const unreadBadge = document.getElementById('unread-notifications-count');
    if (unreadBadge) {
        unreadBadge.textContent = unreadCount;
        unreadBadge.style.display = unreadCount > 0 ? 'inline-block' : 'none';
    }
}

// Setup print functionality
function setupPrintFunctionality() {
    // Set print date
    const now = new Date();
    const formattedDate = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;
    document.getElementById('print-date').textContent = formattedDate;

    // Add print button event listener
    document.getElementById('print-btn').addEventListener('click', function() {
        // Add page title for printing
        const pageTitle = document.createElement('div');
        pageTitle.className = 'page-title print-only';
        pageTitle.style.display = 'none';
        pageTitle.innerHTML = '<h2>لوحة التحكم - نظام إدارة ديون المقصف المدرسي</h2>';
        document.body.insertBefore(pageTitle, document.body.firstChild);

        // Print the page
        window.print();

        // Remove page title after printing
        setTimeout(() => {
            pageTitle.remove();
        }, 1000);
    });
}

// Initialize monthly transactions chart
function initializeMonthlyTransactionsChart() {
    const { transactions } = window.dashboardData;

    // Group transactions by month
    const monthlyData = {};

    // Get last 6 months
    const today = new Date();
    const months = [];

    for (let i = 0; i < 6; i++) {
        const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthKey = `${month.getMonth() + 1}/${month.getFullYear()}`;
        months.unshift(monthKey);

        // Initialize monthly data
        if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = {
                purchases: 0,
                payments: 0
            };
        }
    }

    // Calculate monthly totals
    transactions.forEach(transaction => {
        const date = new Date(transaction.date);
        const monthKey = `${date.getMonth() + 1}/${date.getFullYear()}`;

        // Skip if not in our range
        if (!monthlyData[monthKey]) {
            return;
        }

        const amount = parseFloat(transaction.amount);

        if (transaction.type === 'purchase') {
            monthlyData[monthKey].purchases += amount;
        } else if (transaction.type === 'payment') {
            monthlyData[monthKey].payments += amount;
        }
    });

    // Prepare data for chart
    const labels = months;
    const purchasesData = labels.map(month => monthlyData[month].purchases);
    const paymentsData = labels.map(month => monthlyData[month].payments);

    // Format month labels to be more readable
    const formattedLabels = labels.map(month => {
        const [m, y] = month.split('/');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(m) - 1]} ${y}`;
    });

    // Get canvas element
    const canvas = document.getElementById('monthlyTransactionsChart');

    // Create chart
    if (canvas) {
        // Clear existing chart if any
        if (canvas.chart) {
            canvas.chart.destroy();
        }

        // Create new chart
        canvas.chart = new Chart(canvas, {
            type: 'bar',
            data: {
                labels: formattedLabels,
                datasets: [
                    {
                        label: 'المشتريات',
                        data: purchasesData,
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'المدفوعات',
                        data: paymentsData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'الشهر'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'المبلغ (د.ك)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatKuwaitiCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${formatKuwaitiCurrency(value)}`;
                            }
                        }
                    }
                }
            }
        });
    }
}
