// Student debt management functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Get student ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const studentId = urlParams.get('id');

    if (!studentId) {
        alert('معرف الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }

    // Load student data
    loadStudentData(studentId);

    // Load all students for sidebar
    loadStudentsList(studentId);

    // Handle add purchase form submission
    document.getElementById('add-purchase-form').addEventListener('submit', function(e) {
        e.preventDefault();
        addTransaction(studentId, 'purchase');
    });

    // Handle add payment form submission
    document.getElementById('add-payment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        addTransaction(studentId, 'payment');
    });

    // Handle pay full debt button
    document.getElementById('pay-full-debt-btn').addEventListener('click', function() {
        payFullDebt(studentId);
    });

    // Handle student search
    document.getElementById('searchStudentBtn').addEventListener('click', function() {
        searchStudent();
    });

    // Handle enter key in search box
    document.getElementById('studentSearchBox').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchStudent();
        }
    });
});

// Load student data
function loadStudentData(studentId) {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Find student by ID
    const student = students.find(s => s.id == studentId);

    if (!student) {
        alert('الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }

    // Display student information
    document.getElementById('student-name').textContent = student.name;
    document.getElementById('student-class').textContent = student.class;
    document.getElementById('student-id').textContent = student.student_id;
    document.getElementById('parent-name').textContent = student.parent_name;
    document.getElementById('phone-number').textContent = student.phone_number;

    // Calculate account summary
    let totalPurchases = 0;
    let totalPayments = 0;

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == studentId);

    studentTransactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            totalPurchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            totalPayments += parseFloat(transaction.amount);
        }
    });

    const balance = totalPurchases - totalPayments;

    // Update account summary
    document.getElementById('total-purchases').textContent = formatKuwaitiCurrency(totalPurchases);
    document.getElementById('total-payments').textContent = formatKuwaitiCurrency(totalPayments);

    const balanceElement = document.getElementById('current-balance');
    const balanceStatusElement = document.getElementById('balance-status');
    const fullPaymentInfo = document.getElementById('full-payment-info');
    const fullPaymentAmount = document.getElementById('full-payment-amount');
    const payFullDebtBtn = document.getElementById('pay-full-debt-btn');

    if (balance > 0) {
        balanceElement.textContent = formatKuwaitiCurrency(balance);
        balanceElement.className = 'mb-0 text-danger';
        balanceStatusElement.textContent = 'مدين';
        balanceStatusElement.className = 'text-danger';

        fullPaymentAmount.textContent = formatKuwaitiCurrency(balance);
        fullPaymentInfo.classList.remove('d-none');
        payFullDebtBtn.disabled = false;

        // Set default payment amount to current debt (convert to fils)
        document.getElementById('payment-amount').value = Math.round(balance * 1000);
    } else if (balance < 0) {
        balanceElement.textContent = formatKuwaitiCurrency(Math.abs(balance));
        balanceElement.className = 'mb-0 text-success';
        balanceStatusElement.textContent = 'دائن (رصيد متبقي)';
        balanceStatusElement.className = 'text-success';

        fullPaymentInfo.classList.add('d-none');
        payFullDebtBtn.disabled = true;
    } else {
        balanceElement.textContent = '0 فلس';
        balanceElement.className = 'mb-0';
        balanceStatusElement.textContent = 'متعادل';
        balanceStatusElement.className = 'text-muted';

        fullPaymentInfo.classList.add('d-none');
        payFullDebtBtn.disabled = true;
    }

    // Display transaction history
    displayTransactionHistory(studentId);
}

// Display transaction history
function displayTransactionHistory(studentId) {
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const tableBody = document.getElementById('transactions-table-body');
    tableBody.innerHTML = '';

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == studentId);

    if (studentTransactions.length > 0) {
        // Sort transactions by date (newest first)
        studentTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Display only the last 10 transactions
        const recentTransactions = studentTransactions.slice(0, 10);

        recentTransactions.forEach(transaction => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formatDate(transaction.date)}</td>
                <td>
                    ${transaction.type === 'purchase'
                        ? '<span class="badge bg-danger">مشتريات</span>'
                        : '<span class="badge bg-success">دفعة</span>'}
                </td>
                <td>
                    ${transaction.type === 'purchase'
                        ? `<span class="text-danger">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`
                        : `<span class="text-success">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`}
                </td>
                <td>${transaction.description || '-'}</td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="4" class="text-center">
                لا توجد معاملات مسجلة لهذا الطالب.
            </td>
        `;
        tableBody.appendChild(row);
    }
}

// Add new transaction (purchase or payment)
function addTransaction(studentId, type) {
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));

    // Get form data
    const amountInput = document.getElementById(`${type}-amount`);
    const descriptionInput = document.getElementById(`${type}-description`);

    const amount = parseFloat(amountInput.value);
    const description = descriptionInput.value;

    // Validate amount
    if (isNaN(amount) || amount <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }

    // Get existing transactions from localStorage
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Generate new transaction ID
    const newId = transactions.length > 0 ? Math.max(...transactions.map(transaction => transaction.id)) + 1 : 1;

    // Create new transaction object
    const newTransaction = {
        id: newId,
        student_id: parseInt(studentId),
        type: type,
        amount: amount / 1000, // تحويل من فلس إلى دينار
        description: description,
        date: new Date().toISOString().split('T')[0],
        created_by: currentUser.id,
        created_at: new Date().toISOString()
    };

    // Add new transaction to array
    transactions.push(newTransaction);

    // Save updated transactions array to localStorage
    localStorage.setItem('transactions', JSON.stringify(transactions));

    // Reset form
    amountInput.value = '';
    if (type === 'purchase') {
        descriptionInput.value = '';
    } else {
        descriptionInput.value = 'دفعة نقدية';
    }

    // Show success message
    alert(`تم تسجيل ${type === 'purchase' ? 'المشتريات' : 'الدفعة'} بنجاح`);

    // Reload student data
    loadStudentData(studentId);
}

// Pay full debt
function payFullDebt(studentId) {
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));

    // Get data from localStorage
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Calculate current debt
    let totalPurchases = 0;
    let totalPayments = 0;

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == studentId);

    studentTransactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            totalPurchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            totalPayments += parseFloat(transaction.amount);
        }
    });

    const balance = totalPurchases - totalPayments;

    // Check if there is debt to pay
    if (balance <= 0) {
        alert('لا يوجد دين للسداد');
        return;
    }

    // Confirm payment
    if (confirm(`هل أنت متأكد من سداد الدين بالكامل (${balance.toFixed(3)} دينار)؟`)) {
        // Generate new transaction ID
        const newId = transactions.length > 0 ? Math.max(...transactions.map(transaction => transaction.id)) + 1 : 1;

        // Create new payment transaction
        const newTransaction = {
            id: newId,
            student_id: parseInt(studentId),
            type: 'payment',
            amount: balance,
            description: 'سداد الدين بالكامل',
            date: new Date().toISOString().split('T')[0],
            created_by: currentUser.id,
            created_at: new Date().toISOString()
        };

        // Add new transaction to array
        transactions.push(newTransaction);

        // Save updated transactions array to localStorage
        localStorage.setItem('transactions', JSON.stringify(transactions));

        // Show success message
        alert('تم سداد الدين بالكامل بنجاح');

        // Reload student data
        loadStudentData(studentId);
    }
}

// Load students list for sidebar
function loadStudentsList(currentStudentId) {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const studentListElement = document.querySelector('.student-list');
    studentListElement.innerHTML = '';

    if (students.length > 0) {
        // Sort students by name
        students.sort((a, b) => a.name.localeCompare(b.name)).forEach(student => {
            const listItem = document.createElement('a');
            listItem.href = `student-debt-management.html?id=${student.id}`;
            listItem.className = `list-group-item list-group-item-action ${student.id == currentStudentId ? 'active' : ''}`;
            listItem.innerHTML = `
                <div class="d-flex w-100 justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-user-graduate me-2"></i>
                        ${student.name}
                    </div>
                    <small>${student.class}</small>
                </div>
            `;
            studentListElement.appendChild(listItem);
        });
    } else {
        const listItem = document.createElement('div');
        listItem.className = 'list-group-item text-center';
        listItem.textContent = 'لا يوجد طلاب مسجلين';
        studentListElement.appendChild(listItem);
    }
}

// Search student function
function searchStudent() {
    const searchTerm = document.getElementById('studentSearchBox').value.trim();

    if (!searchTerm) {
        alert('الرجاء إدخال اسم الطالب للبحث');
        return;
    }

    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Search by name (partial match)
    const matchingStudents = students.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.student_id.includes(searchTerm)
    );

    if (matchingStudents.length === 0) {
        alert('لم يتم العثور على طالب بهذا الاسم');
    } else if (matchingStudents.length === 1) {
        // If only one student found, go directly to their page
        window.location.href = `student-debt-management.html?id=${matchingStudents[0].id}`;
    } else {
        // If multiple students found, show a simple modal with results
        let message = 'تم العثور على عدة طلاب. اختر واحداً:\n';
        matchingStudents.forEach((student, index) => {
            message += `${index + 1}. ${student.name} - ${student.class}\n`;
        });

        const selectedIndex = prompt(message, '1');
        if (selectedIndex && !isNaN(selectedIndex) && matchingStudents[parseInt(selectedIndex) - 1]) {
            window.location.href = `student-debt-management.html?id=${matchingStudents[parseInt(selectedIndex) - 1].id}`;
        }
    }
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}
