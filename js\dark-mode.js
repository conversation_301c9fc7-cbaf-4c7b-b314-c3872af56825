// Dark Mode Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Create dark mode toggle button
    createDarkModeToggle();
    
    // Check if dark mode is enabled in localStorage
    const isDarkMode = localStorage.getItem('darkMode') === 'enabled';
    
    // Apply dark mode if enabled
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        updateDarkModeToggleIcon(true);
    }
});

// Create dark mode toggle button
function createDarkModeToggle() {
    const toggleButton = document.createElement('div');
    toggleButton.className = 'dark-mode-toggle';
    toggleButton.innerHTML = '<i class="fas fa-moon"></i>';
    toggleButton.setAttribute('title', 'تبديل الوضع الليلي');
    
    // Add click event listener
    toggleButton.addEventListener('click', toggleDarkMode);
    
    // Append to body
    document.body.appendChild(toggleButton);
}

// Toggle dark mode
function toggleDarkMode() {
    const isDarkMode = document.body.classList.toggle('dark-mode');
    
    // Update localStorage
    if (isDarkMode) {
        localStorage.setItem('darkMode', 'enabled');
    } else {
        localStorage.setItem('darkMode', 'disabled');
    }
    
    // Update toggle icon
    updateDarkModeToggleIcon(isDarkMode);
}

// Update dark mode toggle icon
function updateDarkModeToggleIcon(isDarkMode) {
    const toggleButton = document.querySelector('.dark-mode-toggle');
    if (toggleButton) {
        toggleButton.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
    }
}
