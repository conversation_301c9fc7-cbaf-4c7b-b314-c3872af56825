<?php
// Include authentication
require_once 'config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once 'config/db_grades.php';

// Get total students count
$stmt = $pdo->query('SELECT COUNT(*) as total FROM students');
$totalStudents = $stmt->fetch()['total'] ?? 0;

// Get total subjects count
$stmt = $pdo->query('SELECT COUNT(*) as total FROM subjects');
$totalSubjects = $stmt->fetch()['total'] ?? 0;

// Get total grades count
$stmt = $pdo->query('SELECT COUNT(*) as total FROM grades');
$totalGrades = $stmt->fetch()['total'] ?? 0;

// Get students with highest grades
$stmt = $pdo->query('SELECT s.id, s.name, s.class, AVG(g.score/g.max_score*100) as avg_percentage 
    FROM students s
    JOIN grades g ON s.id = g.student_id
    GROUP BY s.id
    ORDER BY avg_percentage DESC
    LIMIT 5');
$topStudents = $stmt->fetchAll();

// Get recent grades
$stmt = $pdo->query('SELECT g.id, g.score, g.max_score, g.date, s.name as student_name, 
    sub.name as subject_name, at.name as assessment_name
    FROM grades g
    JOIN students s ON g.student_id = s.id
    JOIN subjects sub ON g.subject_id = sub.id
    JOIN assessment_types at ON g.assessment_type_id = at.id
    ORDER BY g.date DESC, g.id DESC
    LIMIT 10');
$recentGrades = $stmt->fetchAll();

// Include header
include 'includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include 'includes/sidebar_grades.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <h2 class="mb-4">لوحة التحكم</h2>
        
        <!-- Dashboard cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card dashboard-card bg-primary text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h5 class="card-title">إجمالي الطلاب</h5>
                        <h2 class="mb-0"><?php echo $totalStudents; ?></h2>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card dashboard-card bg-success text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h5 class="card-title">إجمالي المواد</h5>
                        <h2 class="mb-0"><?php echo $totalSubjects; ?></h2>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card dashboard-card bg-info text-white">
                    <div class="card-body text-center">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="card-title">إجمالي الدرجات</h5>
                        <h2 class="mb-0"><?php echo $totalGrades; ?></h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Top students -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-trophy me-2"></i> الطلاب المتفوقون
                    </div>
                    <div class="card-body">
                        <?php if (count($topStudents) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>الصف</th>
                                            <th>المعدل</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($topStudents as $student): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['class']); ?></td>
                                                <td class="text-success fw-bold"><?php echo number_format($student['avg_percentage'], 2); ?>%</td>
                                                <td>
                                                    <a href="students/view.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">لا توجد بيانات درجات متاحة حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Recent grades -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-history me-2"></i> آخر الدرجات المسجلة
                    </div>
                    <div class="card-body">
                        <?php if (count($recentGrades) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الطالب</th>
                                            <th>المادة</th>
                                            <th>التقييم</th>
                                            <th>الدرجة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentGrades as $grade): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($grade['student_name']); ?></td>
                                                <td><?php echo htmlspecialchars($grade['subject_name']); ?></td>
                                                <td><?php echo htmlspecialchars($grade['assessment_name']); ?></td>
                                                <td>
                                                    <?php 
                                                    $percentage = ($grade['score'] / $grade['max_score']) * 100;
                                                    $textClass = $percentage >= 90 ? 'text-success' : ($percentage >= 70 ? 'text-primary' : ($percentage >= 50 ? 'text-warning' : 'text-danger'));
                                                    ?>
                                                    <span class="<?php echo $textClass; ?> fw-bold">
                                                        <?php echo $grade['score'] . '/' . $grade['max_score']; ?>
                                                        (<?php echo number_format($percentage, 0); ?>%)
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-center">لا توجد درجات مسجلة حديثاً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick actions -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-bolt me-2"></i> إجراءات سريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="students/add.php" class="btn btn-outline-primary w-100 p-3">
                            <i class="fas fa-user-plus mb-2 d-block" style="font-size: 2rem;"></i>
                            إضافة طالب جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="subjects/add.php" class="btn btn-outline-success w-100 p-3">
                            <i class="fas fa-book mb-2 d-block" style="font-size: 2rem;"></i>
                            إضافة مادة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="grades/add.php" class="btn btn-outline-info w-100 p-3">
                            <i class="fas fa-plus-circle mb-2 d-block" style="font-size: 2rem;"></i>
                            تسجيل درجات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="reports/generate.php" class="btn btn-outline-secondary w-100 p-3">
                            <i class="fas fa-file-alt mb-2 d-block" style="font-size: 2rem;"></i>
                            إنشاء تقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
