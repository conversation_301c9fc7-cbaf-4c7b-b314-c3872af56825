// Import and Export functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Export Students to Excel
    document.getElementById('export-students-excel').addEventListener('click', function() {
        exportStudentsToExcel();
    });

    // Export Students to PDF
    document.getElementById('export-students-pdf').addEventListener('click', function() {
        exportStudentsToPDF();
    });

    // Export Transactions to Excel
    document.getElementById('export-transactions-excel').addEventListener('click', function() {
        exportTransactionsToExcel();
    });

    // Export Transactions to PDF
    document.getElementById('export-transactions-pdf').addEventListener('click', function() {
        exportTransactionsToPDF();
    });

    // Download Template
    document.getElementById('download-template').addEventListener('click', function(e) {
        e.preventDefault();
        downloadStudentTemplate();
    });

    // Import Students
    document.getElementById('import-students-btn').addEventListener('click', function() {
        importStudents();
    });

    // Backup Data
    document.getElementById('backup-data-btn').addEventListener('click', function() {
        backupData();
    });

    // Restore Data
    document.getElementById('restore-data-btn').addEventListener('click', function() {
        restoreData();
    });
});

// Export Students to Excel
function exportStudentsToExcel() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    if (students.length === 0) {
        alert('لا يوجد طلاب لتصديرهم');
        return;
    }

    // Calculate student debts
    const studentDebts = {};

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            total_purchases: 0,
            total_payments: 0,
            balance: 0
        };
    });

    // Calculate debts from transactions
    transactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].total_purchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].total_payments += parseFloat(transaction.amount);
        }
    });

    // Calculate balance
    Object.keys(studentDebts).forEach(id => {
        studentDebts[id].balance = studentDebts[id].total_purchases - studentDebts[id].total_payments;
    });

    // Prepare data for export
    const exportData = students.map(student => {
        return {
            'اسم الطالب': student.name,
            'الصف': student.class,
            'رقم الهوية': student.student_id,
            'ولي الأمر': student.parent_name,
            'رقم الجوال': student.phone_number,
            'إجمالي المشتريات': formatKuwaitiCurrency(studentDebts[student.id].total_purchases),
            'إجمالي المدفوعات': formatKuwaitiCurrency(studentDebts[student.id].total_payments),
            'الرصيد': formatKuwaitiCurrency(studentDebts[student.id].balance)
        };
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'الطلاب');

    // Generate Excel file
    XLSX.writeFile(workbook, 'بيانات_الطلاب.xlsx');
}

// Export Students to PDF
function exportStudentsToPDF() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    if (students.length === 0) {
        alert('لا يوجد طلاب لتصديرهم');
        return;
    }

    // Calculate student debts
    const studentDebts = {};

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            total_purchases: 0,
            total_payments: 0,
            balance: 0
        };
    });

    // Calculate debts from transactions
    transactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].total_purchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].total_payments += parseFloat(transaction.amount);
        }
    });

    // Calculate balance
    Object.keys(studentDebts).forEach(id => {
        studentDebts[id].balance = studentDebts[id].total_purchases - studentDebts[id].total_payments;
    });

    // Create PDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
    });

    // Set right-to-left mode
    doc.setR2L(true);

    // Add title
    doc.setFontSize(18);
    doc.text('تقرير بيانات الطلاب', doc.internal.pageSize.width / 2, 15, { align: 'center' });

    // Add date
    doc.setFontSize(12);
    doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-KW')}`, doc.internal.pageSize.width / 2, 22, { align: 'center' });

    // Prepare table data
    const tableData = students.map(student => [
        student.name,
        student.class,
        student.student_id,
        student.parent_name,
        student.phone_number,
        formatKuwaitiCurrency(studentDebts[student.id].total_purchases),
        formatKuwaitiCurrency(studentDebts[student.id].total_payments),
        formatKuwaitiCurrency(studentDebts[student.id].balance)
    ]);

    // Add table
    doc.autoTable({
        head: [['اسم الطالب', 'الصف', 'رقم الهوية', 'ولي الأمر', 'رقم الجوال', 'إجمالي المشتريات', 'إجمالي المدفوعات', 'الرصيد']],
        body: tableData,
        startY: 30,
        theme: 'grid',
        styles: {
            font: 'courier',
            halign: 'right',
            textColor: [0, 0, 0],
            lineColor: [0, 0, 0],
            lineWidth: 0.1
        },
        headStyles: {
            fillColor: [41, 128, 185],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [240, 240, 240]
        }
    });

    // Save PDF
    doc.save('بيانات_الطلاب.pdf');
}

// Export Transactions to Excel
function exportTransactionsToExcel() {
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];

    if (transactions.length === 0) {
        alert('لا توجد معاملات لتصديرها');
        return;
    }

    // Prepare data for export
    const exportData = transactions.map(transaction => {
        const student = students.find(s => s.id === transaction.student_id) || { name: 'غير معروف', class: '-' };
        const user = users.find(u => u.id === transaction.created_by) || { username: 'غير معروف' };

        return {
            'التاريخ': new Date(transaction.date).toLocaleDateString('ar-KW'),
            'اسم الطالب': student.name,
            'الصف': student.class,
            'نوع المعاملة': transaction.type === 'purchase' ? 'مشتريات' : 'دفعة',
            'المبلغ': formatKuwaitiCurrency(transaction.amount),
            'الوصف': transaction.description || '-',
            'بواسطة': user.username,
            'تاريخ الإنشاء': new Date(transaction.created_at).toLocaleString('ar-KW')
        };
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'المعاملات');

    // Generate Excel file
    XLSX.writeFile(workbook, 'سجل_المعاملات.xlsx');
}

// Export Transactions to PDF
function exportTransactionsToPDF() {
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];

    if (transactions.length === 0) {
        alert('لا توجد معاملات لتصديرها');
        return;
    }

    // Create PDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
    });

    // Set right-to-left mode
    doc.setR2L(true);

    // Add title
    doc.setFontSize(18);
    doc.text('سجل المعاملات', doc.internal.pageSize.width / 2, 15, { align: 'center' });

    // Add date
    doc.setFontSize(12);
    doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-KW')}`, doc.internal.pageSize.width / 2, 22, { align: 'center' });

    // Prepare table data
    const tableData = transactions.map(transaction => {
        const student = students.find(s => s.id === transaction.student_id) || { name: 'غير معروف', class: '-' };
        const user = users.find(u => u.id === transaction.created_by) || { username: 'غير معروف' };

        return [
            new Date(transaction.date).toLocaleDateString('ar-KW'),
            student.name,
            student.class,
            transaction.type === 'purchase' ? 'مشتريات' : 'دفعة',
            formatKuwaitiCurrency(transaction.amount),
            transaction.description || '-',
            user.username
        ];
    });

    // Add table
    doc.autoTable({
        head: [['التاريخ', 'اسم الطالب', 'الصف', 'نوع المعاملة', 'المبلغ', 'الوصف', 'بواسطة']],
        body: tableData,
        startY: 30,
        theme: 'grid',
        styles: {
            font: 'courier',
            halign: 'right',
            textColor: [0, 0, 0],
            lineColor: [0, 0, 0],
            lineWidth: 0.1
        },
        headStyles: {
            fillColor: [41, 128, 185],
            textColor: [255, 255, 255],
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [240, 240, 240]
        }
    });

    // Save PDF
    doc.save('سجل_المعاملات.pdf');
}

// Download Student Template
function downloadStudentTemplate() {
    // Create template data
    const templateData = [
        {
            'اسم الطالب': 'أحمد محمد',
            'الصف': 'الصف الثالث',
            'رقم الهوية': '1001',
            'ولي الأمر': 'محمد أحمد',
            'رقم الجوال': '0501234567'
        },
        {
            'اسم الطالب': 'سارة خالد',
            'الصف': 'الصف الثاني',
            'رقم الهوية': '1002',
            'ولي الأمر': 'خالد سعيد',
            'رقم الجوال': '0509876543'
        }
    ];

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(templateData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'نموذج_الطلاب');

    // Generate Excel file
    XLSX.writeFile(workbook, 'نموذج_استيراد_الطلاب.xlsx');
}

// Import Students
function importStudents() {
    const fileInput = document.getElementById('import-students-file');
    const file = fileInput.files[0];

    if (!file) {
        alert('الرجاء اختيار ملف');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Get first sheet
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];

            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                alert('الملف لا يحتوي على بيانات');
                return;
            }

            // Get existing students
            const existingStudents = JSON.parse(localStorage.getItem('students')) || [];
            const existingStudentIds = existingStudents.map(student => student.student_id);

            // Process imported data
            const newStudents = [];
            const skippedStudents = [];
            const invalidStudents = [];

            jsonData.forEach(row => {
                // Check if row has all required fields
                if (row['اسم الطالب'] && row['الصف'] && row['رقم الهوية'] && row['ولي الأمر'] && row['رقم الجوال']) {
                    // Check if student_id already exists
                    if (existingStudentIds.includes(row['رقم الهوية'])) {
                        skippedStudents.push(row['اسم الطالب']);
                    } else {
                        // Generate new student ID
                        const newId = existingStudents.length > 0 ? Math.max(...existingStudents.map(student => student.id)) + 1 + newStudents.length : 1 + newStudents.length;

                        // Create new student object
                        const newStudent = {
                            id: newId,
                            name: row['اسم الطالب'],
                            class: row['الصف'],
                            student_id: row['رقم الهوية'],
                            parent_name: row['ولي الأمر'],
                            phone_number: row['رقم الجوال'],
                            created_at: new Date().toISOString()
                        };

                        newStudents.push(newStudent);
                        existingStudentIds.push(row['رقم الهوية']);
                    }
                } else {
                    invalidStudents.push(row['اسم الطالب'] || 'طالب بدون اسم');
                }
            });

            // Add new students to existing students
            const updatedStudents = [...existingStudents, ...newStudents];

            // Save updated students to localStorage
            localStorage.setItem('students', JSON.stringify(updatedStudents));

            // Show results
            let message = `تم استيراد ${newStudents.length} طالب بنجاح.`;

            if (skippedStudents.length > 0) {
                message += `\nتم تجاهل ${skippedStudents.length} طالب لوجود رقم هوية مكرر.`;
            }

            if (invalidStudents.length > 0) {
                message += `\nتم تجاهل ${invalidStudents.length} طالب لنقص في البيانات المطلوبة.`;
            }

            alert(message);

            // Reset file input
            fileInput.value = '';
        } catch (error) {
            console.error('Error importing students:', error);
            alert('حدث خطأ أثناء استيراد البيانات. تأكد من أن الملف بالتنسيق الصحيح.');
        }
    };

    reader.readAsArrayBuffer(file);
}

// Backup Data
function backupData() {
    // Get all data from localStorage
    const backupData = {
        students: JSON.parse(localStorage.getItem('students')) || [],
        transactions: JSON.parse(localStorage.getItem('transactions')) || [],
        users: JSON.parse(localStorage.getItem('users')) || []
    };

    // Convert to JSON string
    const jsonString = JSON.stringify(backupData);

    // Create blob
    const blob = new Blob([jsonString], { type: 'application/json' });

    // Create download link
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = `نسخة_احتياطية_${new Date().toISOString().split('T')[0]}.json`;

    // Trigger download
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// Restore Data
function restoreData() {
    const fileInput = document.getElementById('restore-file');
    const file = fileInput.files[0];

    if (!file) {
        alert('الرجاء اختيار ملف');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);

            // Validate backup data
            if (!backupData.students || !backupData.transactions || !backupData.users) {
                alert('ملف النسخة الاحتياطية غير صالح');
                return;
            }

            // Confirm restore
            if (confirm('سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية. هل أنت متأكد؟')) {
                // Restore data
                localStorage.setItem('students', JSON.stringify(backupData.students));
                localStorage.setItem('transactions', JSON.stringify(backupData.transactions));
                localStorage.setItem('users', JSON.stringify(backupData.users));

                alert('تم استعادة البيانات بنجاح');

                // Reset file input
                fileInput.value = '';
            }
        } catch (error) {
            console.error('Error restoring data:', error);
            alert('حدث خطأ أثناء استعادة البيانات. تأكد من أن الملف بالتنسيق الصحيح.');
        }
    };

    reader.readAsText(file);
}
