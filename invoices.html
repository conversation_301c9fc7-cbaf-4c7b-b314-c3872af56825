<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الفواتير وسندات القبض - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-invoice me-2"></i> الفواتير وسندات القبض
                        </a>
                        <a href="import-export.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-excel me-2"></i> استيراد/تصدير البيانات
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">الفواتير وسندات القبض</h2>
                    <button id="reset-documents-data" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sync-alt me-1"></i> إعادة تهيئة البيانات
                    </button>
                </div>

                <!-- Tabs -->
                <ul class="nav nav-tabs mb-4" id="invoicesTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="true">
                            <i class="fas fa-file-invoice me-1"></i> فواتير المشتريات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="receipts-tab" data-bs-toggle="tab" data-bs-target="#receipts" type="button" role="tab" aria-controls="receipts" aria-selected="false">
                            <i class="fas fa-receipt me-1"></i> سندات القبض
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab" aria-controls="create" aria-selected="false">
                            <i class="fas fa-plus-circle me-1"></i> إنشاء فاتورة/سند جديد
                        </button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="invoicesTabsContent">
                    <!-- Invoices Tab -->
                    <div class="tab-pane fade show active" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
                        <!-- Filter -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="invoice-student-filter" class="form-label">الطالب</label>
                                        <select class="form-select" id="invoice-student-filter">
                                            <option value="">جميع الطلاب</option>
                                            <!-- Will be populated by JavaScript -->
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="invoice-date-from" class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="invoice-date-from">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="invoice-date-to" class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="invoice-date-to">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-light" id="reset-invoice-filter">إعادة تعيين</button>
                                    <button class="btn btn-primary" id="apply-invoice-filter">تطبيق الفلتر</button>
                                </div>
                            </div>
                        </div>

                        <!-- Invoices List -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>الطالب</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices-table-body">
                                    <!-- Will be populated by JavaScript -->
                                    <tr>
                                        <td colspan="6" class="text-center">جاري تحميل البيانات...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Receipts Tab -->
                    <div class="tab-pane fade" id="receipts" role="tabpanel" aria-labelledby="receipts-tab">
                        <!-- Filter -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="receipt-student-filter" class="form-label">الطالب</label>
                                        <select class="form-select" id="receipt-student-filter">
                                            <option value="">جميع الطلاب</option>
                                            <!-- Will be populated by JavaScript -->
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="receipt-date-from" class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="receipt-date-from">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="receipt-date-to" class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="receipt-date-to">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-light" id="reset-receipt-filter">إعادة تعيين</button>
                                    <button class="btn btn-primary" id="apply-receipt-filter">تطبيق الفلتر</button>
                                </div>
                            </div>
                        </div>

                        <!-- Receipts List -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم السند</th>
                                        <th>التاريخ</th>
                                        <th>الطالب</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="receipts-table-body">
                                    <!-- Will be populated by JavaScript -->
                                    <tr>
                                        <td colspan="6" class="text-center">جاري تحميل البيانات...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Create Tab -->
                    <div class="tab-pane fade" id="create" role="tabpanel" aria-labelledby="create-tab">
                        <div class="card">
                            <div class="card-body">
                                <form id="create-document-form">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="document-type" class="form-label">نوع المستند <span class="text-danger">*</span></label>
                                            <select class="form-select" id="document-type" required>
                                                <option value="">اختر نوع المستند...</option>
                                                <option value="invoice">فاتورة مشتريات</option>
                                                <option value="receipt">سند قبض</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="document-student" class="form-label">الطالب <span class="text-danger">*</span></label>
                                            <select class="form-select" id="document-student" required>
                                                <option value="">اختر الطالب...</option>
                                                <!-- Will be populated by JavaScript -->
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="document-date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="document-date" required>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="document-amount" class="form-label">المبلغ (فلس) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="document-amount" step="1" min="1" required>
                                            <div class="form-text">أدخل المبلغ بالفلس (1000 فلس = 1 دينار)</div>
                                        </div>
                                    </div>

                                    <div class="row" id="payment-method-row" style="display: none;">
                                        <div class="col-md-6 mb-3">
                                            <label for="payment-method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                            <select class="form-select" id="payment-method">
                                                <option value="cash">نقداً</option>
                                                <option value="bank_transfer">تحويل بنكي</option>
                                                <option value="check">شيك</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="reference-number" class="form-label">رقم المرجع</label>
                                            <input type="text" class="form-control" id="reference-number" placeholder="رقم الشيك أو التحويل...">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="document-description" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="document-description" rows="3"></textarea>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                                        <button type="submit" class="btn btn-primary">إنشاء وطباعة</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Preview Modal -->
    <div class="modal fade" id="documentPreviewModal" tabindex="-1" aria-labelledby="documentPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="documentPreviewModalLabel">معاينة المستند</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="document-preview-content">
                    <!-- Will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="print-document-btn">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/invoices.js"></script>

    <!-- Direct Action Functions -->
    <script>
        // Make sure viewDocument and printDocument are available globally
        function viewDocument(documentId) {
            if (window.viewDocument) {
                window.viewDocument(documentId);
            } else {
                console.error('viewDocument function is not defined');
                alert('حدث خطأ: وظيفة عرض المستند غير معرفة');
            }
        }

        function printDocument(documentId) {
            if (window.printDocument) {
                window.printDocument(documentId);
            } else {
                console.error('printDocument function is not defined');
                alert('حدث خطأ: وظيفة طباعة المستند غير معرفة');
            }
        }
    </script>
</body>
</html>
