<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get all classes for filter
$stmt = $pdo->query('SELECT DISTINCT class FROM students ORDER BY class');
$classes = $stmt->fetchAll();

// Get students with their debt amount
$query = 'SELECT s.*, 
    COALESCE(SUM(CASE WHEN t.type = "purchase" THEN t.amount ELSE 0 END), 0) - 
    COALESCE(SUM(CASE WHEN t.type = "payment" THEN t.amount ELSE 0 END), 0) as debt 
    FROM students s
    LEFT JOIN transactions t ON s.id = t.student_id
    GROUP BY s.id
    ORDER BY s.name';
$stmt = $pdo->query($query);
$students = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>إدارة الطلاب</h2>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة طالب جديد
            </a>
        </div>
        
        <!-- Search and filter -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" id="studentSearch" class="form-control" placeholder="ابحث باسم الطالب أو رقم الهوية...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select id="classFilter" class="form-select">
                            <option value="">جميع الصفوف</option>
                            <?php foreach ($classes as $class): ?>
                                <option value="<?php echo htmlspecialchars($class['class']); ?>">
                                    <?php echo htmlspecialchars($class['class']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Students table -->
        <div class="card">
            <div class="card-body">
                <?php if (count($students) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>الصف</th>
                                    <th>رقم الهوية</th>
                                    <th>ولي الأمر</th>
                                    <th>رقم الجوال</th>
                                    <th>الدين</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr class="student-row" data-class="<?php echo htmlspecialchars($student['class']); ?>">
                                        <td class="student-name"><?php echo htmlspecialchars($student['name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['class']); ?></td>
                                        <td class="student-id"><?php echo htmlspecialchars($student['student_id']); ?></td>
                                        <td><?php echo htmlspecialchars($student['parent_name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['phone_number']); ?></td>
                                        <td>
                                            <?php if ($student['debt'] > 0): ?>
                                                <span class="text-danger fw-bold"><?php echo number_format($student['debt'], 2); ?> ريال</span>
                                            <?php elseif ($student['debt'] < 0): ?>
                                                <span class="text-success fw-bold"><?php echo number_format(abs($student['debt']), 2); ?> ريال (رصيد)</span>
                                            <?php else: ?>
                                                <span class="text-muted">0.00 ريال</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="view.php?id=<?php echo $student['id']; ?>" class="btn btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-primary" data-bs-toggle="tooltip" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="../transactions/new.php?student_id=<?php echo $student['id']; ?>" class="btn btn-danger" data-bs-toggle="tooltip" title="تسجيل مشتريات">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </a>
                                                <a href="../transactions/payment.php?student_id=<?php echo $student['id']; ?>" class="btn btn-success" data-bs-toggle="tooltip" title="تسجيل دفعة">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        لا يوجد طلاب مسجلين حالياً. <a href="add.php">إضافة طالب جديد</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
