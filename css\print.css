/* Print Styles */

@media print {
    /* General styles */
    body {
        background-color: white !important;
        color: black !important;
        font-size: 12pt;
        width: 100%;
        margin: 0;
        padding: 0;
    }
    
    /* Hide unnecessary elements */
    nav, 
    .sidebar, 
    .btn,
    footer,
    .dark-mode-toggle,
    .no-print {
        display: none !important;
    }
    
    /* Show full width content */
    .container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .col-md-9 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
    }
    
    /* Add page title */
    .page-title {
        display: block !important;
        text-align: center;
        font-size: 18pt;
        font-weight: bold;
        margin-bottom: 20px;
        border-bottom: 1px solid #000;
        padding-bottom: 10px;
    }
    
    /* Improve table appearance */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        page-break-inside: auto !important;
    }
    
    tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }
    
    th {
        background-color: #f0f0f0 !important;
        color: black !important;
        border: 1px solid #ddd !important;
        padding: 8px !important;
    }
    
    td {
        border: 1px solid #ddd !important;
        padding: 8px !important;
    }
    
    /* Ensure cards print properly */
    .card {
        border: 1px solid #ddd !important;
        margin-bottom: 20px !important;
        break-inside: avoid !important;
    }
    
    .card-header {
        background-color: #f0f0f0 !important;
        color: black !important;
        border-bottom: 1px solid #ddd !important;
        padding: 10px !important;
        font-weight: bold !important;
    }
    
    .card-body {
        padding: 10px !important;
    }
    
    /* Ensure text colors are visible */
    .text-danger, .text-success, .text-primary, .text-info, .text-warning {
        color: black !important;
    }
    
    /* Add print-specific classes */
    .print-only {
        display: block !important;
    }
    
    /* Ensure page breaks */
    .page-break {
        page-break-before: always !important;
    }
    
    /* Adjust badges */
    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background-color: transparent !important;
    }
    
    /* Adjust charts */
    canvas {
        max-width: 100% !important;
        height: auto !important;
    }
    
    /* Add footer with page numbers */
    @page {
        margin: 1cm;
    }
    
    @page :first {
        margin-top: 2cm;
    }
    
    /* Print header and footer */
    .print-header, .print-footer {
        display: block !important;
    }
    
    .print-header {
        position: fixed;
        top: 0;
        width: 100%;
        height: 100px;
        text-align: center;
        border-bottom: 1px solid #000;
    }
    
    .print-footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 50px;
        text-align: center;
        border-top: 1px solid #000;
    }
    
    /* Print specific styles for different pages */
    
    /* Dashboard */
    .dashboard-card {
        width: 25% !important;
        float: left !important;
        margin: 0 !important;
        padding: 10px !important;
    }
    
    /* Student details */
    .student-info-card {
        width: 100% !important;
        margin-bottom: 20px !important;
    }
    
    /* Transactions */
    .transaction-table {
        width: 100% !important;
    }
    
    /* Reports */
    .report-section {
        margin-bottom: 30px !important;
        break-inside: avoid !important;
    }
    
    /* Invoices */
    .invoice-container {
        padding: 20px !important;
        border: 1px solid #000 !important;
    }
    
    .invoice-header {
        border-bottom: 2px solid #000 !important;
        padding-bottom: 20px !important;
        margin-bottom: 20px !important;
    }
    
    .invoice-footer {
        border-top: 2px solid #000 !important;
        padding-top: 20px !important;
        margin-top: 20px !important;
    }
    
    /* Installment plans */
    .installment-plan-details {
        break-inside: avoid !important;
    }
}
