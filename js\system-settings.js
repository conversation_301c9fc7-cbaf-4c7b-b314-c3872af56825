// System Settings functionality

// Global variables
let currentUser;
let systemSettings = {};
let defaultSettings = {
    school: {
        name: 'مدرسة نموذجية',
        logo: 'img/default-logo.png',
        address: 'الكويت',
        phone: '+96512345678',
        email: '<EMAIL>',
        academicYear: '2023-2024'
    },
    currency: {
        name: 'دينار كويتي',
        code: 'KWD',
        symbol: 'د.ك',
        smallCurrencyName: 'فلس',
        smallCurrencyRatio: 1000,
        showSmallCurrency: true
    },
    notifications: {
        enabled: true,
        whatsappEnabled: true,
        defaultCountryCode: '+965',
        debtNotificationThreshold: 5.000
    },
    backup: {
        autoBackup: true,
        frequency: 'weekly'
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    } else {
        // Redirect to login if not logged in
        window.location.href = 'index.html';
        return;
    }
    
    // Check if user has admin role
    if (currentUser.role !== 'admin') {
        alert('ليس لديك صلاحية الوصول إلى هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });
    
    // Load settings
    loadSettings();
    
    // Setup event listeners
    setupEventListeners();
});

// Load settings from localStorage
function loadSettings() {
    // Get settings from localStorage or use defaults
    const storedSettings = localStorage.getItem('systemSettings');
    
    if (storedSettings) {
        systemSettings = JSON.parse(storedSettings);
    } else {
        systemSettings = JSON.parse(JSON.stringify(defaultSettings)); // Deep copy
        localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    }
    
    // Populate form fields
    populateSchoolSettings();
    populateCurrencySettings();
    populateNotificationSettings();
    populateBackupSettings();
}

// Populate school settings form
function populateSchoolSettings() {
    const school = systemSettings.school;
    
    document.getElementById('school-name').value = school.name;
    document.getElementById('academic-year').value = school.academicYear;
    document.getElementById('logo-preview').src = school.logo;
    document.getElementById('school-address').value = school.address;
    document.getElementById('school-phone').value = school.phone;
    document.getElementById('school-email').value = school.email;
    
    // Handle logo file input change
    document.getElementById('school-logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('logo-preview').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
}

// Populate currency settings form
function populateCurrencySettings() {
    const currency = systemSettings.currency;
    
    document.getElementById('currency-name').value = currency.name;
    document.getElementById('currency-code').value = currency.code;
    document.getElementById('currency-symbol').value = currency.symbol;
    document.getElementById('small-currency-name').value = currency.smallCurrencyName;
    document.getElementById('small-currency-ratio').value = currency.smallCurrencyRatio;
    document.getElementById('show-small-currency').checked = currency.showSmallCurrency;
}

// Populate notification settings form
function populateNotificationSettings() {
    const notifications = systemSettings.notifications;
    
    document.getElementById('enable-notifications').checked = notifications.enabled;
    document.getElementById('enable-whatsapp-notifications').checked = notifications.whatsappEnabled;
    document.getElementById('default-country-code').value = notifications.defaultCountryCode;
    document.getElementById('debt-notification-threshold').value = notifications.debtNotificationThreshold;
}

// Populate backup settings form
function populateBackupSettings() {
    const backup = systemSettings.backup;
    
    document.getElementById('enable-auto-backup').checked = backup.autoBackup;
    document.getElementById('backup-frequency').value = backup.frequency;
}

// Setup event listeners
function setupEventListeners() {
    // Form submission
    document.getElementById('system-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });
    
    // Backup now button
    document.getElementById('backup-now-btn').addEventListener('click', createBackup);
    
    // Restore backup button
    document.getElementById('restore-backup-btn').addEventListener('click', function() {
        document.getElementById('backup-file').click();
    });
    
    // Backup file input change
    document.getElementById('backup-file').addEventListener('change', restoreBackup);
    
    // Reset settings button
    document.getElementById('reset-settings-btn').addEventListener('click', function() {
        showConfirmationModal(
            'هل أنت متأكد من أنك تريد إعادة تعيين الإعدادات الافتراضية؟',
            resetSettings
        );
    });
    
    // Clear all data button
    document.getElementById('clear-all-data-btn').addEventListener('click', function() {
        showConfirmationModal(
            'هل أنت متأكد من أنك تريد حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
            clearAllData
        );
    });
}

// Save settings
function saveSettings() {
    // Get values from school settings form
    systemSettings.school = {
        name: document.getElementById('school-name').value,
        logo: document.getElementById('logo-preview').src,
        address: document.getElementById('school-address').value,
        phone: document.getElementById('school-phone').value,
        email: document.getElementById('school-email').value,
        academicYear: document.getElementById('academic-year').value
    };
    
    // Get values from currency settings form
    systemSettings.currency = {
        name: document.getElementById('currency-name').value,
        code: document.getElementById('currency-code').value,
        symbol: document.getElementById('currency-symbol').value,
        smallCurrencyName: document.getElementById('small-currency-name').value,
        smallCurrencyRatio: parseInt(document.getElementById('small-currency-ratio').value),
        showSmallCurrency: document.getElementById('show-small-currency').checked
    };
    
    // Get values from notification settings form
    systemSettings.notifications = {
        enabled: document.getElementById('enable-notifications').checked,
        whatsappEnabled: document.getElementById('enable-whatsapp-notifications').checked,
        defaultCountryCode: document.getElementById('default-country-code').value,
        debtNotificationThreshold: parseFloat(document.getElementById('debt-notification-threshold').value)
    };
    
    // Get values from backup settings form
    systemSettings.backup = {
        autoBackup: document.getElementById('enable-auto-backup').checked,
        frequency: document.getElementById('backup-frequency').value
    };
    
    // Save settings to localStorage
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    
    // Show success message
    alert('تم حفظ الإعدادات بنجاح');
    
    // Log activity
    logActivity('update', 'settings', 'تم تحديث إعدادات النظام');
}

// Create backup
function createBackup() {
    // Get all data from localStorage
    const backup = {
        systemSettings: JSON.parse(localStorage.getItem('systemSettings')) || defaultSettings,
        students: JSON.parse(localStorage.getItem('students')) || [],
        transactions: JSON.parse(localStorage.getItem('transactions')) || [],
        installmentPlans: JSON.parse(localStorage.getItem('installmentPlans')) || [],
        users: JSON.parse(localStorage.getItem('users')) || [],
        timestamp: new Date().toISOString(),
        version: '1.0'
    };
    
    // Convert to JSON string
    const backupJson = JSON.stringify(backup);
    
    // Create a blob and download link
    const blob = new Blob([backupJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup_${formatDateForFilename(new Date())}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // Show success message
    alert('تم إنشاء نسخة احتياطية بنجاح');
    
    // Log activity
    logActivity('create', 'backup', 'تم إنشاء نسخة احتياطية');
}

// Restore backup
function restoreBackup(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backup = JSON.parse(e.target.result);
            
            // Validate backup format
            if (!backup.version || !backup.timestamp) {
                throw new Error('تنسيق النسخة الاحتياطية غير صالح');
            }
            
            // Confirm restore
            showConfirmationModal(
                `هل أنت متأكد من أنك تريد استعادة النسخة الاحتياطية المؤرخة بتاريخ ${formatDate(backup.timestamp)}؟ سيتم استبدال جميع البيانات الحالية.`,
                function() {
                    // Restore data
                    if (backup.systemSettings) localStorage.setItem('systemSettings', JSON.stringify(backup.systemSettings));
                    if (backup.students) localStorage.setItem('students', JSON.stringify(backup.students));
                    if (backup.transactions) localStorage.setItem('transactions', JSON.stringify(backup.transactions));
                    if (backup.installmentPlans) localStorage.setItem('installmentPlans', JSON.stringify(backup.installmentPlans));
                    if (backup.users) localStorage.setItem('users', JSON.stringify(backup.users));
                    
                    // Show success message
                    alert('تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل الصفحة.');
                    
                    // Log activity
                    logActivity('update', 'restore', 'تم استعادة نسخة احتياطية');
                    
                    // Reload page
                    window.location.reload();
                }
            );
        } catch (error) {
            alert('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// Reset settings
function resetSettings() {
    // Reset settings to defaults
    systemSettings = JSON.parse(JSON.stringify(defaultSettings)); // Deep copy
    localStorage.setItem('systemSettings', JSON.stringify(systemSettings));
    
    // Show success message
    alert('تم إعادة تعيين الإعدادات الافتراضية بنجاح. سيتم إعادة تحميل الصفحة.');
    
    // Log activity
    logActivity('update', 'settings', 'تم إعادة تعيين الإعدادات الافتراضية');
    
    // Reload page
    window.location.reload();
}

// Clear all data
function clearAllData() {
    // Keep settings and users
    const settings = localStorage.getItem('systemSettings');
    const users = localStorage.getItem('users');
    
    // Clear localStorage
    localStorage.clear();
    
    // Restore settings and users
    if (settings) localStorage.setItem('systemSettings', settings);
    if (users) localStorage.setItem('users', users);
    
    // Show success message
    alert('تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
    
    // Log activity
    logActivity('delete', 'data', 'تم حذف جميع البيانات');
    
    // Reload page
    window.location.reload();
}

// Show confirmation modal
function showConfirmationModal(message, confirmCallback) {
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    document.getElementById('confirmation-message').textContent = message;
    
    // Remove previous event listener
    const confirmBtn = document.getElementById('confirm-action-btn');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // Add new event listener
    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        confirmCallback();
    });
    
    modal.show();
}

// Log activity
function logActivity(type, target, description) {
    // Get activity log from localStorage
    let activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
    
    // Add new activity
    activityLog.push({
        id: Date.now(),
        timestamp: new Date().toISOString(),
        userId: currentUser.id,
        username: currentUser.username,
        type: type,
        target: target,
        description: description,
        ip: '127.0.0.1' // In a real app, this would be the actual IP
    });
    
    // Save activity log to localStorage
    localStorage.setItem('activityLog', JSON.stringify(activityLog));
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
}

// Format date for filename
function formatDateForFilename(date) {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}_${date.getHours().toString().padStart(2, '0')}-${date.getMinutes().toString().padStart(2, '0')}`;
}
