<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="css/dark-mode.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="installments.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-alt me-2"></i> نظام الأقساط
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice me-2"></i> الفواتير وسندات القبض
                        </a>
                        <a href="import-export.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-excel me-2"></i> استيراد/تصدير البيانات
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="whatsapp-settings.html" class="list-group-item list-group-item-action">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة الإشعارات</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createNotificationModal">
                            <i class="fas fa-plus-circle me-1"></i> إنشاء إشعار جديد
                        </button>
                        <button type="button" class="btn btn-success ms-2" id="generate-notifications-btn">
                            <i class="fas fa-sync me-1"></i> توليد إشعارات تلقائية
                        </button>
                    </div>
                </div>

                <!-- Notification filters -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-filter me-2"></i> تصفية الإشعارات
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="notification-type-filter" class="form-label">نوع الإشعار</label>
                                <select id="notification-type-filter" class="form-select">
                                    <option value="all">جميع الأنواع</option>
                                    <option value="debt">ديون متأخرة</option>
                                    <option value="installment">أقساط مستحقة</option>
                                    <option value="payment">دفعات مستلمة</option>
                                    <option value="system">إشعارات النظام</option>
                                    <option value="custom">إشعارات مخصصة</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="notification-status-filter" class="form-label">حالة الإشعار</label>
                                <select id="notification-status-filter" class="form-select">
                                    <option value="all">جميع الحالات</option>
                                    <option value="unread">غير مقروءة</option>
                                    <option value="read">مقروءة</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="notification-date-filter" class="form-label">تاريخ الإشعار</label>
                                <select id="notification-date-filter" class="form-select">
                                    <option value="all">جميع الفترات</option>
                                    <option value="today">اليوم</option>
                                    <option value="week">هذا الأسبوع</option>
                                    <option value="month">هذا الشهر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications list -->
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-bell me-2"></i> قائمة الإشعارات
                        </div>
                        <div>
                            <button class="btn btn-sm btn-light" id="mark-all-read-btn">
                                <i class="fas fa-check-double me-1"></i> تعيين الكل كمقروء
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="notifications-list">
                            <!-- Will be populated by JavaScript -->
                            <div class="text-center p-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري تحميل الإشعارات...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center" id="notifications-pagination">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Create Notification Modal -->
    <div class="modal fade" id="createNotificationModal" tabindex="-1" aria-labelledby="createNotificationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createNotificationModalLabel">إنشاء إشعار جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="create-notification-form">
                        <div class="mb-3">
                            <label for="notification-type" class="form-label">نوع الإشعار</label>
                            <select id="notification-type" class="form-select" required>
                                <option value="">اختر نوع الإشعار...</option>
                                <option value="debt">دين متأخر</option>
                                <option value="installment">قسط مستحق</option>
                                <option value="payment">دفعة مستلمة</option>
                                <option value="custom">إشعار مخصص</option>
                            </select>
                        </div>

                        <div class="mb-3" id="student-selection-container">
                            <label for="notification-student" class="form-label">الطالب</label>
                            <select id="notification-student" class="form-select">
                                <option value="">اختر الطالب...</option>
                                <!-- Will be populated by JavaScript -->
                            </select>
                        </div>

                        <div class="mb-3" id="class-selection-container" style="display: none;">
                            <label for="notification-class" class="form-label">الصف</label>
                            <select id="notification-class" class="form-select">
                                <option value="">اختر الصف...</option>
                                <!-- Will be populated by JavaScript -->
                            </select>
                            <div class="form-text">اختر الصف لإرسال الإشعار لجميع طلاب الصف</div>
                        </div>

                        <div class="mb-3">
                            <label for="notification-title" class="form-label">عنوان الإشعار</label>
                            <input type="text" class="form-control" id="notification-title" required>
                        </div>

                        <div class="mb-3">
                            <label for="notification-message" class="form-label">نص الإشعار</label>
                            <textarea class="form-control" id="notification-message" rows="4" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="notification-priority" class="form-label">الأولوية</label>
                            <select id="notification-priority" class="form-select">
                                <option value="normal">عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">طرق الإرسال</label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="notification-send-sms">
                                <label class="form-check-label" for="notification-send-sms">
                                    إرسال رسالة نصية (SMS)
                                </label>
                                <div class="form-text">سيتم إرسال رسالة نصية إلى رقم ولي الأمر</div>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notification-send-whatsapp">
                                <label class="form-check-label" for="notification-send-whatsapp">
                                    <i class="fab fa-whatsapp text-success me-1"></i> إرسال عبر واتساب
                                </label>
                                <div class="form-text">سيتم إرسال رسالة عبر واتساب إلى رقم ولي الأمر</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="create-notification-btn">إنشاء الإشعار</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Details Modal -->
    <div class="modal fade" id="notificationDetailsModal" tabindex="-1" aria-labelledby="notificationDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notificationDetailsModalLabel">تفاصيل الإشعار</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="notification-details-content">
                    <!-- Will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="notification-action-btn">إجراء</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/sample-data.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/dark-mode.js"></script>
</body>
</html>
