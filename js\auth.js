// Authentication and user management

// Default admin user
const defaultUser = {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
};

// Initialize users in localStorage if not exists
if (!localStorage.getItem('users')) {
    localStorage.setItem('users', JSON.stringify([defaultUser]));
}

// Initialize students in localStorage if not exists
if (!localStorage.getItem('students')) {
    const sampleStudents = [
        {
            id: 1,
            name: 'أحمد محمد',
            class: 'الصف الثالث',
            student_id: '1001',
            parent_name: 'محمد أحمد',
            phone_number: '0501234567',
            created_at: new Date().toISOString()
        },
        {
            id: 2,
            name: 'سارة خالد',
            class: 'الصف الثاني',
            student_id: '1002',
            parent_name: 'خالد سعيد',
            phone_number: '0509876543',
            created_at: new Date().toISOString()
        },
        {
            id: 3,
            name: 'عمر علي',
            class: 'الصف الأول',
            student_id: '1003',
            parent_name: 'علي عمر',
            phone_number: '0507654321',
            created_at: new Date().toISOString()
        },
        {
            id: 4,
            name: 'فاطمة أحمد',
            class: 'الصف الثالث',
            student_id: '1004',
            parent_name: 'أحمد محمود',
            phone_number: '0501122334',
            created_at: new Date().toISOString()
        },
        {
            id: 5,
            name: 'محمد سعيد',
            class: 'الصف الثاني',
            student_id: '1005',
            parent_name: 'سعيد محمد',
            phone_number: '0505544332',
            created_at: new Date().toISOString()
        }
    ];
    localStorage.setItem('students', JSON.stringify(sampleStudents));
}

// Initialize transactions in localStorage if not exists
if (!localStorage.getItem('transactions')) {
    const today = new Date().toISOString().split('T')[0];
    const sampleTransactions = [
        {
            id: 1,
            student_id: 1,
            type: 'purchase',
            amount: 15.00,
            description: 'وجبة غداء',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 2,
            student_id: 1,
            type: 'purchase',
            amount: 5.00,
            description: 'عصير',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 3,
            student_id: 2,
            type: 'purchase',
            amount: 20.00,
            description: 'وجبة غداء وعصير',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 4,
            student_id: 3,
            type: 'purchase',
            amount: 10.00,
            description: 'سندويش',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 5,
            student_id: 4,
            type: 'purchase',
            amount: 25.00,
            description: 'وجبة كاملة',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 6,
            student_id: 5,
            type: 'purchase',
            amount: 15.00,
            description: 'وجبة غداء',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 7,
            student_id: 2,
            type: 'payment',
            amount: 10.00,
            description: 'دفعة جزئية',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        },
        {
            id: 8,
            student_id: 4,
            type: 'payment',
            amount: 25.00,
            description: 'دفعة كاملة',
            date: today,
            created_by: 1,
            created_at: new Date().toISOString()
        }
    ];
    localStorage.setItem('transactions', JSON.stringify(sampleTransactions));
}

// Handle login form submission
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorElement = document.getElementById('login-error');
            
            // Get users from localStorage
            const users = JSON.parse(localStorage.getItem('users'));
            
            // Find user with matching email
            const user = users.find(u => u.email === email);
            
            if (user && user.password === password) {
                // Store user info in sessionStorage
                sessionStorage.setItem('currentUser', JSON.stringify({
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: user.role
                }));
                
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
            } else {
                // Show error message
                errorElement.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                errorElement.classList.remove('d-none');
            }
        });
    }
    
    // Check if user is already logged in
    const currentUser = sessionStorage.getItem('currentUser');
    if (currentUser && window.location.pathname.endsWith('index.html')) {
        window.location.href = 'dashboard.html';
    }
    
    // If not logged in and trying to access protected page
    if (!currentUser && !window.location.pathname.endsWith('index.html')) {
        window.location.href = 'index.html';
    }
});
