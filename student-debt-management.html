<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة دين الطالب - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        .action-card {
            transition: transform 0.3s;
            border-radius: 10px;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .transaction-history {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <!-- Search Student -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-search me-2"></i> بحث عن طالب
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" id="studentSearchBox" class="form-control" placeholder="اكتب اسم الطالب...">
                            <button class="btn btn-primary" type="button" id="searchStudentBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Menu -->
                <div class="card sidebar mb-4">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice me-2"></i> الفواتير وسندات القبض
                        </a>
                        <a href="import-export.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-excel me-2"></i> استيراد/تصدير البيانات
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                    </div>
                </div>

                <!-- Students List -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-users me-2"></i> قائمة الطلاب
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush student-list" style="max-height: 300px; overflow-y: auto;">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة دين الطالب</h2>
                    <a href="students.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                    </a>
                </div>

                <!-- Student info card -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-user-graduate me-2"></i> معلومات الطالب
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4 id="student-name" class="mb-3">اسم الطالب</h4>
                                <p><strong>الصف:</strong> <span id="student-class"></span></p>
                                <p><strong>رقم الهوية:</strong> <span id="student-id"></span></p>
                                <p><strong>ولي الأمر:</strong> <span id="parent-name"></span></p>
                                <p><strong>رقم الجوال:</strong> <span id="phone-number"></span></p>
                            </div>
                            <div class="col-md-6 text-center">
                                <div class="border rounded p-3 mb-3">
                                    <h5>الرصيد الحالي</h5>
                                    <h2 id="current-balance" class="mb-0">0.00 دينار</h2>
                                    <small id="balance-status" class="text-muted">متعادل</small>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <p><strong>إجمالي المشتريات:</strong></p>
                                        <p id="total-purchases" class="text-danger">0.00 دينار</p>
                                    </div>
                                    <div class="col-6">
                                        <p><strong>إجمالي المدفوعات:</strong></p>
                                        <p id="total-payments" class="text-success">0.00 دينار</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action cards -->
                <div class="row mb-4">
                    <!-- Add new purchase -->
                    <div class="col-md-4 mb-3">
                        <div class="card action-card h-100">
                            <div class="card-header bg-danger text-white">
                                <i class="fas fa-shopping-cart me-2"></i> إضافة مشتريات
                            </div>
                            <div class="card-body">
                                <form id="add-purchase-form">
                                    <div class="mb-3">
                                        <label for="purchase-amount" class="form-label">المبلغ (فلس)</label>
                                        <input type="number" class="form-control" id="purchase-amount" step="1" min="1" required>
                                        <div class="form-text">أدخل المبلغ بالفلس (1000 فلس = 1 دينار)</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="purchase-description" class="form-label">الوصف</label>
                                        <input type="text" class="form-control" id="purchase-description" placeholder="وجبة غداء، عصير، إلخ">
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-plus-circle me-1"></i> إضافة
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Add payment -->
                    <div class="col-md-4 mb-3">
                        <div class="card action-card h-100">
                            <div class="card-header bg-success text-white">
                                <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعة
                            </div>
                            <div class="card-body">
                                <form id="add-payment-form">
                                    <div class="mb-3">
                                        <label for="payment-amount" class="form-label">المبلغ (فلس)</label>
                                        <input type="number" class="form-control" id="payment-amount" step="1" min="1" required>
                                        <div class="form-text">أدخل المبلغ بالفلس (1000 فلس = 1 دينار)</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="payment-description" class="form-label">الوصف</label>
                                        <input type="text" class="form-control" id="payment-description" value="دفعة نقدية">
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-check-circle me-1"></i> تسجيل
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Pay full debt -->
                    <div class="col-md-4 mb-3">
                        <div class="card action-card h-100">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-hand-holding-usd me-2"></i> سداد الدين بالكامل
                            </div>
                            <div class="card-body">
                                <p class="mb-3">اضغط على الزر أدناه لتسجيل دفعة بكامل مبلغ الدين الحالي.</p>
                                <div id="full-payment-info" class="alert alert-info mb-3">
                                    المبلغ المطلوب: <span id="full-payment-amount">0.00</span> دينار
                                </div>
                                <div class="d-grid">
                                    <button id="pay-full-debt-btn" class="btn btn-primary">
                                        <i class="fas fa-check-double me-1"></i> سداد الدين بالكامل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent transactions -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-history me-2"></i> آخر المعاملات
                    </div>
                    <div class="card-body">
                        <div class="transaction-history">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody id="transactions-table-body">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/student-debt-management.js"></script>
</body>
</html>
