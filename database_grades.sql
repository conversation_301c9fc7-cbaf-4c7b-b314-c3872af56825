-- Create database
CREATE DATABASE IF NOT EXISTS school_grades CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_grades;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'teacher', 'supervisor') NOT NULL DEFAULT 'teacher',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    class VARCHAR(50) NOT NULL,
    section VARCHAR(20) NOT NULL,
    student_id VARCHAR(50) NOT NULL UNIQUE,
    parent_name VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create subjects table
CREATE TABLE IF NOT EXISTS subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create assessment_types table
CREATE TABLE IF NOT EXISTS assessment_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    weight DECIMAL(5, 2) NOT NULL,
    max_score DECIMAL(5, 2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create grades table
CREATE TABLE IF NOT EXISTS grades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    assessment_type_id INT NOT NULL,
    score DECIMAL(5, 2) NOT NULL,
    max_score DECIMAL(5, 2) NOT NULL,
    date DATE NOT NULL,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (assessment_type_id) REFERENCES assessment_types(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2y$10$8tGmGEOg1LLgSgJwQYXlleWVjxjSUvHfJN1zIlHEYvGhsxJQZgX7.', 'admin');

-- Insert sample subjects
INSERT INTO subjects (name, code, description) VALUES
('الرياضيات', 'MATH101', 'مادة الرياضيات للمرحلة الابتدائية'),
('العلوم', 'SCI101', 'مادة العلوم للمرحلة الابتدائية'),
('اللغة العربية', 'ARB101', 'مادة اللغة العربية للمرحلة الابتدائية'),
('اللغة الإنجليزية', 'ENG101', 'مادة اللغة الإنجليزية للمرحلة الابتدائية'),
('التربية الإسلامية', 'ISL101', 'مادة التربية الإسلامية للمرحلة الابتدائية');

-- Insert sample assessment types
INSERT INTO assessment_types (name, weight, max_score, description) VALUES
('اختبار فصلي أول', 20.00, 20.00, 'اختبار منتصف الفصل الدراسي الأول'),
('اختبار نهائي أول', 30.00, 30.00, 'اختبار نهاية الفصل الدراسي الأول'),
('واجبات', 10.00, 10.00, 'درجات الواجبات المنزلية'),
('أنشطة', 10.00, 10.00, 'درجات الأنشطة الصفية'),
('اختبار فصلي ثاني', 20.00, 20.00, 'اختبار منتصف الفصل الدراسي الثاني'),
('اختبار نهائي ثاني', 30.00, 30.00, 'اختبار نهاية الفصل الدراسي الثاني');

-- Insert sample students
INSERT INTO students (name, class, section, student_id, parent_name, phone_number) VALUES
('أحمد محمد', 'الصف الثالث', 'أ', '1001', 'محمد أحمد', '0501234567'),
('سارة خالد', 'الصف الثاني', 'ب', '1002', 'خالد سعيد', '0509876543'),
('عمر علي', 'الصف الأول', 'أ', '1003', 'علي عمر', '0507654321'),
('فاطمة أحمد', 'الصف الثالث', 'ب', '1004', 'أحمد محمود', '0501122334'),
('محمد سعيد', 'الصف الثاني', 'أ', '1005', 'سعيد محمد', '0505544332');

-- Insert sample grades
INSERT INTO grades (student_id, subject_id, assessment_type_id, score, max_score, date, notes, created_by) VALUES
(1, 1, 1, 18.00, 20.00, CURDATE(), 'أداء ممتاز', 1),
(1, 2, 1, 16.50, 20.00, CURDATE(), 'جيد جداً', 1),
(2, 1, 1, 15.00, 20.00, CURDATE(), 'جيد', 1),
(2, 3, 1, 19.00, 20.00, CURDATE(), 'ممتاز', 1),
(3, 4, 1, 14.00, 20.00, CURDATE(), 'جيد', 1),
(4, 5, 1, 20.00, 20.00, CURDATE(), 'ممتاز', 1),
(5, 1, 1, 17.50, 20.00, CURDATE(), 'جيد جداً', 1),
(1, 1, 3, 9.00, 10.00, CURDATE(), 'واجبات كاملة', 1),
(2, 1, 3, 8.50, 10.00, CURDATE(), 'واجبات جيدة', 1),
(3, 1, 3, 7.00, 10.00, CURDATE(), 'بعض الواجبات ناقصة', 1);
