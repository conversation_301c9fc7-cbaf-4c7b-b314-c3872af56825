// Installments management functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

// Global variable for current user
let currentUser;

// Chart colors
const chartColors = [
    'rgba(255, 99, 132, 0.7)',   // Red
    'rgba(54, 162, 235, 0.7)',   // Blue
    'rgba(255, 206, 86, 0.7)',   // Yellow
    'rgba(75, 192, 192, 0.7)',   // Green
    'rgba(153, 102, 255, 0.7)',  // Purple
    'rgba(255, 159, 64, 0.7)',   // Orange
    'rgba(199, 199, 199, 0.7)',  // Gray
    'rgba(83, 102, 255, 0.7)',   // Indigo
    'rgba(255, 99, 255, 0.7)',   // Pink
    'rgba(0, 162, 150, 0.7)',    // Teal
];

// Initialize installment plans in localStorage if not exists
if (!localStorage.getItem('installmentPlans')) {
    localStorage.setItem('installmentPlans', JSON.stringify([]));
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set default date for first payment
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    document.getElementById('first-payment-date').valueAsDate = tomorrow;

    // Load students for dropdown
    loadStudentsDropdown();

    // Load installment plans
    loadInstallmentPlans();

    // Check for student_id in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const studentId = urlParams.get('student_id');

    if (studentId) {
        // Set student in dropdown and update debt
        document.getElementById('student-select').value = studentId;
        updateStudentDebt();

        // Open create installment plan modal
        const modal = new bootstrap.Modal(document.getElementById('createInstallmentPlanModal'));
        modal.show();
    }

    // Handle student selection change
    document.getElementById('student-select').addEventListener('change', function() {
        updateStudentDebt();
    });

    // Handle installment amount and count change
    document.getElementById('installment-amount').addEventListener('input', updateInstallmentInfo);
    document.getElementById('installment-count').addEventListener('input', updateInstallmentInfo);
    document.getElementById('first-payment-date').addEventListener('change', updateInstallmentInfo);
    document.getElementById('payment-frequency').addEventListener('change', updateInstallmentInfo);

    // Handle create installment plan button
    document.getElementById('create-installment-plan-btn').addEventListener('click', createInstallmentPlan);

    // Handle print installment plan button
    document.getElementById('print-installment-plan-btn').addEventListener('click', function() {
        window.print();
    });

    // Handle confirm payment button
    document.getElementById('confirm-payment-btn').addEventListener('click', confirmInstallmentPayment);
});

// Load students dropdown
function loadStudentsDropdown() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const studentSelect = document.getElementById('student-select');

    // Sort students by name
    students.sort((a, b) => a.name.localeCompare(b.name));

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        studentSelect.appendChild(option);
    });
}

// Update student debt display
function updateStudentDebt() {
    const studentId = document.getElementById('student-select').value;
    const currentDebtInput = document.getElementById('current-debt');

    if (!studentId) {
        currentDebtInput.value = '';
        return;
    }

    // Calculate student debt
    const debt = calculateStudentDebt(studentId);

    // Update debt display
    currentDebtInput.value = formatKuwaitiCurrency(debt);

    // Set default installment amount to current debt
    if (debt > 0) {
        document.getElementById('installment-amount').value = debt.toFixed(3);
        updateInstallmentInfo();
    } else {
        document.getElementById('installment-amount').value = '';
    }
}

// Calculate student debt
function calculateStudentDebt(studentId) {
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    let debt = 0;

    transactions.forEach(transaction => {
        if (transaction.student_id == studentId) {
            if (transaction.type === 'purchase') {
                debt += parseFloat(transaction.amount);
            } else if (transaction.type === 'payment') {
                debt -= parseFloat(transaction.amount);
            }
        }
    });

    return debt;
}

// Update installment info
function updateInstallmentInfo() {
    const amount = parseFloat(document.getElementById('installment-amount').value) || 0;
    const count = parseInt(document.getElementById('installment-count').value) || 1;
    const firstPaymentDate = document.getElementById('first-payment-date').value;
    const frequency = document.getElementById('payment-frequency').value;

    // Calculate installment value
    const installmentValue = amount / count;
    document.getElementById('installment-value').textContent = installmentValue.toFixed(3);

    // Calculate last payment date
    if (firstPaymentDate) {
        const lastPaymentDate = calculateLastPaymentDate(firstPaymentDate, count - 1, frequency);
        document.getElementById('last-payment-date').textContent = formatDate(lastPaymentDate);
    }
}

// Calculate last payment date
function calculateLastPaymentDate(firstPaymentDate, additionalPayments, frequency) {
    const date = new Date(firstPaymentDate);

    switch (frequency) {
        case 'weekly':
            date.setDate(date.getDate() + (additionalPayments * 7));
            break;
        case 'biweekly':
            date.setDate(date.getDate() + (additionalPayments * 14));
            break;
        case 'monthly':
            date.setMonth(date.getMonth() + additionalPayments);
            break;
    }

    return date;
}

// Create installment plan
function createInstallmentPlan() {
    // Get form values
    const studentId = document.getElementById('student-select').value;
    const amount = parseFloat(document.getElementById('installment-amount').value);
    const count = parseInt(document.getElementById('installment-count').value);
    const firstPaymentDate = document.getElementById('first-payment-date').value;
    const frequency = document.getElementById('payment-frequency').value;
    const notes = document.getElementById('installment-notes').value;

    // Validate form
    if (!studentId || isNaN(amount) || amount <= 0 || isNaN(count) || count < 2 || !firstPaymentDate) {
        alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
        return;
    }

    // Get student info
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const student = students.find(s => s.id == studentId);

    if (!student) {
        alert('الطالب غير موجود');
        return;
    }

    // Calculate installment value
    const installmentValue = amount / count;

    // Generate installments
    const installments = [];
    let currentDate = new Date(firstPaymentDate);

    for (let i = 0; i < count; i++) {
        // Clone the date to avoid reference issues
        const paymentDate = new Date(currentDate.getTime());

        installments.push({
            index: i + 1,
            amount: installmentValue,
            due_date: paymentDate.toISOString().split('T')[0],
            status: 'pending',
            payment_date: null,
            payment_method: null,
            notes: null
        });

        // Calculate next payment date
        switch (frequency) {
            case 'weekly':
                currentDate.setDate(currentDate.getDate() + 7);
                break;
            case 'biweekly':
                currentDate.setDate(currentDate.getDate() + 14);
                break;
            case 'monthly':
                currentDate.setMonth(currentDate.getMonth() + 1);
                break;
        }
    }

    // Create installment plan object
    const installmentPlan = {
        id: Date.now(),
        student_id: parseInt(studentId),
        student_name: student.name,
        student_class: student.class,
        total_amount: amount,
        installment_count: count,
        frequency: frequency,
        start_date: firstPaymentDate,
        end_date: installments[count - 1].due_date,
        notes: notes,
        status: 'active',
        created_at: new Date().toISOString(),
        created_by: currentUser ? currentUser.id : 1,
        installments: installments
    };

    // Save installment plan
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];
    installmentPlans.push(installmentPlan);
    localStorage.setItem('installmentPlans', JSON.stringify(installmentPlans));

    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('createInstallmentPlanModal'));
    modal.hide();
    document.getElementById('installment-plan-form').reset();

    // Reload installment plans
    loadInstallmentPlans();

    // Show success message
    alert('تم إنشاء خطة الأقساط بنجاح');
}

// Load installment plans
function loadInstallmentPlans() {
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];

    // Load active plans
    loadActivePlans(installmentPlans.filter(plan => plan.status === 'active'));

    // Load upcoming payments
    loadUpcomingPayments(installmentPlans.filter(plan => plan.status === 'active'));

    // Load completed plans
    loadCompletedPlans(installmentPlans.filter(plan => plan.status === 'completed'));
}

// Load active plans
function loadActivePlans(activePlans) {
    const tableBody = document.getElementById('active-plans-table');
    tableBody.innerHTML = '';

    if (activePlans.length > 0) {
        activePlans.forEach(plan => {
            // Calculate paid and remaining amounts
            const paidInstallments = plan.installments.filter(installment => installment.status === 'paid');
            const paidAmount = paidInstallments.reduce((sum, installment) => sum + installment.amount, 0);
            const remainingAmount = plan.total_amount - paidAmount;

            // Find next payment date
            const pendingInstallments = plan.installments.filter(installment => installment.status === 'pending');
            const nextPaymentDate = pendingInstallments.length > 0 ? pendingInstallments[0].due_date : '-';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${plan.student_name} - ${plan.student_class}</td>
                <td>${formatKuwaitiCurrency(plan.total_amount)}</td>
                <td>${plan.installment_count}</td>
                <td>${formatKuwaitiCurrency(paidAmount)} (${paidInstallments.length}/${plan.installment_count})</td>
                <td>${formatKuwaitiCurrency(remainingAmount)}</td>
                <td>${formatDate(nextPaymentDate)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-info view-plan" data-id="${plan.id}" data-bs-toggle="tooltip" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-success pay-next-installment" data-id="${plan.id}" data-bs-toggle="tooltip" title="دفع القسط التالي">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);

            // Add event listeners
            row.querySelector('.view-plan').addEventListener('click', function() {
                viewInstallmentPlan(plan.id);
            });

            row.querySelector('.pay-next-installment').addEventListener('click', function() {
                payNextInstallment(plan.id);
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="7" class="text-center">لا توجد خطط أقساط نشطة</td>';
        tableBody.appendChild(row);
    }
}

// Load upcoming payments
function loadUpcomingPayments(activePlans) {
    const tableBody = document.getElementById('upcoming-payments-table');
    tableBody.innerHTML = '';

    // Get all pending installments from active plans
    const upcomingPayments = [];

    activePlans.forEach(plan => {
        plan.installments.forEach(installment => {
            if (installment.status === 'pending') {
                upcomingPayments.push({
                    plan_id: plan.id,
                    student_name: plan.student_name,
                    student_class: plan.student_class,
                    installment_index: installment.index,
                    amount: installment.amount,
                    due_date: installment.due_date
                });
            }
        });
    });

    // Sort by due date (earliest first)
    upcomingPayments.sort((a, b) => new Date(a.due_date) - new Date(b.due_date));

    if (upcomingPayments.length > 0) {
        upcomingPayments.forEach(payment => {
            // Calculate days remaining
            const dueDate = new Date(payment.due_date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const diffTime = dueDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            let daysRemainingText = '';
            let daysRemainingClass = '';

            if (diffDays < 0) {
                daysRemainingText = `متأخر ${Math.abs(diffDays)} يوم`;
                daysRemainingClass = 'text-danger';
            } else if (diffDays === 0) {
                daysRemainingText = 'اليوم';
                daysRemainingClass = 'text-warning';
            } else {
                daysRemainingText = `${diffDays} يوم`;
                daysRemainingClass = diffDays <= 3 ? 'text-warning' : 'text-success';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${payment.student_name} - ${payment.student_class}</td>
                <td>${payment.installment_index}</td>
                <td>${formatKuwaitiCurrency(payment.amount)}</td>
                <td>${formatDate(payment.due_date)}</td>
                <td class="${daysRemainingClass}">${daysRemainingText}</td>
                <td>
                    <button class="btn btn-sm btn-success pay-installment"
                            data-plan-id="${payment.plan_id}"
                            data-installment-index="${payment.installment_index - 1}"
                            data-bs-toggle="tooltip"
                            title="تسجيل دفع">
                        <i class="fas fa-money-bill-wave"></i> دفع
                    </button>
                </td>
            `;
            tableBody.appendChild(row);

            // Add event listener
            row.querySelector('.pay-installment').addEventListener('click', function() {
                const planId = this.getAttribute('data-plan-id');
                const installmentIndex = this.getAttribute('data-installment-index');
                openPaymentModal(planId, installmentIndex);
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">لا توجد دفعات قادمة</td>';
        tableBody.appendChild(row);
    }
}

// Load completed plans
function loadCompletedPlans(completedPlans) {
    const tableBody = document.getElementById('completed-plans-table');
    tableBody.innerHTML = '';

    if (completedPlans.length > 0) {
        completedPlans.forEach(plan => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${plan.student_name} - ${plan.student_class}</td>
                <td>${formatKuwaitiCurrency(plan.total_amount)}</td>
                <td>${plan.installment_count}</td>
                <td>${formatDate(plan.start_date)}</td>
                <td>${formatDate(plan.end_date)}</td>
                <td>
                    <button class="btn btn-sm btn-info view-plan" data-id="${plan.id}" data-bs-toggle="tooltip" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </td>
            `;
            tableBody.appendChild(row);

            // Add event listener
            row.querySelector('.view-plan').addEventListener('click', function() {
                viewInstallmentPlan(plan.id);
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">لا توجد خطط أقساط مكتملة</td>';
        tableBody.appendChild(row);
    }
}

// View installment plan
function viewInstallmentPlan(planId) {
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];
    const plan = installmentPlans.find(p => p.id == planId);

    if (!plan) {
        alert('خطة الأقساط غير موجودة');
        return;
    }

    // Calculate paid and remaining amounts
    const paidInstallments = plan.installments.filter(installment => installment.status === 'paid');
    const paidAmount = paidInstallments.reduce((sum, installment) => sum + installment.amount, 0);
    const remainingAmount = plan.total_amount - paidAmount;

    // Get frequency text
    let frequencyText = '';
    switch (plan.frequency) {
        case 'weekly':
            frequencyText = 'أسبوعي';
            break;
        case 'biweekly':
            frequencyText = 'كل أسبوعين';
            break;
        case 'monthly':
            frequencyText = 'شهري';
            break;
    }

    // Create plan details HTML
    const detailsContainer = document.getElementById('installment-plan-details');
    detailsContainer.innerHTML = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h5>معلومات الطالب</h5>
                <p class="mb-1"><strong>الاسم:</strong> ${plan.student_name}</p>
                <p class="mb-1"><strong>الصف:</strong> ${plan.student_class}</p>
            </div>
            <div class="col-md-6">
                <h5>معلومات خطة الأقساط</h5>
                <p class="mb-1"><strong>المبلغ الإجمالي:</strong> ${formatKuwaitiCurrency(plan.total_amount)}</p>
                <p class="mb-1"><strong>عدد الأقساط:</strong> ${plan.installment_count}</p>
                <p class="mb-1"><strong>قيمة القسط:</strong> ${formatKuwaitiCurrency(plan.total_amount / plan.installment_count)}</p>
                <p class="mb-1"><strong>تكرار الدفع:</strong> ${frequencyText}</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <h5>حالة الخطة</h5>
                <p class="mb-1"><strong>تاريخ البدء:</strong> ${formatDate(plan.start_date)}</p>
                <p class="mb-1"><strong>تاريخ الانتهاء المتوقع:</strong> ${formatDate(plan.end_date)}</p>
                <p class="mb-1"><strong>الحالة:</strong> ${plan.status === 'active' ? '<span class="badge bg-success">نشطة</span>' : '<span class="badge bg-secondary">مكتملة</span>'}</p>
            </div>
            <div class="col-md-6">
                <h5>ملخص المدفوعات</h5>
                <p class="mb-1"><strong>المبلغ المدفوع:</strong> ${formatKuwaitiCurrency(paidAmount)} (${paidInstallments.length}/${plan.installment_count})</p>
                <p class="mb-1"><strong>المبلغ المتبقي:</strong> ${formatKuwaitiCurrency(remainingAmount)}</p>
                <div class="progress mt-2" style="height: 20px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: ${(paidInstallments.length / plan.installment_count) * 100}%;"
                         aria-valuenow="${(paidInstallments.length / plan.installment_count) * 100}" aria-valuemin="0" aria-valuemax="100">
                        ${Math.round((paidInstallments.length / plan.installment_count) * 100)}%
                    </div>
                </div>
            </div>
        </div>

        <h5>تفاصيل الأقساط</h5>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>رقم القسط</th>
                        <th>المبلغ</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>تاريخ الدفع</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    ${plan.installments.map(installment => `
                        <tr>
                            <td>${installment.index}</td>
                            <td>${formatKuwaitiCurrency(installment.amount)}</td>
                            <td>${formatDate(installment.due_date)}</td>
                            <td>
                                ${installment.status === 'paid'
                                    ? '<span class="badge bg-success">مدفوع</span>'
                                    : isOverdue(installment.due_date)
                                        ? '<span class="badge bg-danger">متأخر</span>'
                                        : '<span class="badge bg-warning">قيد الانتظار</span>'}
                            </td>
                            <td>${installment.payment_date ? formatDate(installment.payment_date) : '-'}</td>
                            <td>${getPaymentMethodText(installment.payment_method)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        ${plan.notes ? `
            <div class="mt-3">
                <h5>ملاحظات</h5>
                <p>${plan.notes}</p>
            </div>
        ` : ''}
    `;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('viewInstallmentPlanModal'));
    modal.show();
}

// Pay next installment
function payNextInstallment(planId) {
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];
    const plan = installmentPlans.find(p => p.id == planId);

    if (!plan) {
        alert('خطة الأقساط غير موجودة');
        return;
    }

    // Find first pending installment
    const pendingInstallmentIndex = plan.installments.findIndex(installment => installment.status === 'pending');

    if (pendingInstallmentIndex === -1) {
        alert('جميع الأقساط مدفوعة بالفعل');
        return;
    }

    // Open payment modal
    openPaymentModal(planId, pendingInstallmentIndex);
}

// Open payment modal
function openPaymentModal(planId, installmentIndex) {
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];
    const plan = installmentPlans.find(p => p.id == planId);

    if (!plan) {
        alert('خطة الأقساط غير موجودة');
        return;
    }

    const installment = plan.installments[installmentIndex];

    if (!installment) {
        alert('القسط غير موجود');
        return;
    }

    // Set payment modal values
    document.getElementById('payment-plan-id').value = planId;
    document.getElementById('payment-installment-index').value = installmentIndex;
    document.getElementById('payment-student-name').value = `${plan.student_name} - ${plan.student_class}`;
    document.getElementById('payment-amount').value = installment.amount.toFixed(3);
    document.getElementById('payment-date').valueAsDate = new Date();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('payInstallmentModal'));
    modal.show();
}

// Confirm installment payment
function confirmInstallmentPayment() {
    const planId = document.getElementById('payment-plan-id').value;
    const installmentIndex = document.getElementById('payment-installment-index').value;
    const paymentDate = document.getElementById('payment-date').value;
    const paymentMethod = document.getElementById('payment-method').value;
    const paymentNotes = document.getElementById('payment-notes').value;

    // Validate form
    if (!paymentDate) {
        alert('يرجى إدخال تاريخ الدفع');
        return;
    }

    // Get installment plans
    const installmentPlans = JSON.parse(localStorage.getItem('installmentPlans')) || [];
    const planIndex = installmentPlans.findIndex(p => p.id == planId);

    if (planIndex === -1) {
        alert('خطة الأقساط غير موجودة');
        return;
    }

    const plan = installmentPlans[planIndex];
    const installment = plan.installments[installmentIndex];

    if (!installment) {
        alert('القسط غير موجود');
        return;
    }

    // Update installment status
    installment.status = 'paid';
    installment.payment_date = paymentDate;
    installment.payment_method = paymentMethod;
    installment.notes = paymentNotes;

    // Check if all installments are paid
    const allPaid = plan.installments.every(inst => inst.status === 'paid');

    if (allPaid) {
        plan.status = 'completed';
    }

    // Update installment plans in localStorage
    localStorage.setItem('installmentPlans', JSON.stringify(installmentPlans));

    // Create payment transaction
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    const newTransaction = {
        id: Date.now(),
        student_id: plan.student_id,
        type: 'payment',
        amount: installment.amount,
        description: `دفع القسط رقم ${installment.index} من خطة الأقساط`,
        date: paymentDate,
        created_by: currentUser ? currentUser.id : 1,
        created_at: new Date().toISOString()
    };

    transactions.push(newTransaction);
    localStorage.setItem('transactions', JSON.stringify(transactions));

    // Close modal and reset form
    const modal = bootstrap.Modal.getInstance(document.getElementById('payInstallmentModal'));
    modal.hide();
    document.getElementById('pay-installment-form').reset();

    // Reload installment plans
    loadInstallmentPlans();

    // Show success message
    alert('تم تسجيل الدفع بنجاح');
}

// Check if date is overdue
function isOverdue(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return date < today;
}

// Get payment method text
function getPaymentMethodText(method) {
    if (!method) return '-';

    switch (method) {
        case 'cash':
            return 'نقداً';
        case 'bank_transfer':
            return 'تحويل بنكي';
        case 'check':
            return 'شيك';
        case 'other':
            return 'أخرى';
        default:
            return method;
    }
}

// Format date
function formatDate(dateString) {
    if (!dateString || dateString === '-') return '-';

    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}
