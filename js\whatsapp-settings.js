// WhatsApp Settings and Integration

// Global variables
let currentUser;
let whatsappSettings = {};
let whatsappConnection = {
    status: 'disconnected', // disconnected, connecting, connected
    phone: '',
    name: '',
    connectedAt: null
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Load Twilio settings
    loadTwilioSettings();

    // Load sent messages
    loadSentMessages();
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    } else {
        // Redirect to login if not logged in
        window.location.href = 'index.html';
        return;
    }

    // Check if user has admin role
    if (currentUser.role !== 'admin') {
        alert('ليس لديك صلاحية الوصول إلى هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Load WhatsApp settings
    loadWhatsAppSettings();

    // Load WhatsApp connection status
    loadWhatsAppConnectionStatus();

    // Load classes for bulk messages
    loadClassesForBulkMessages();

    // Load students for custom selection
    loadStudentsForCustomSelection();

    // Setup event listeners
    setupEventListeners();
});

// Load WhatsApp settings
function loadWhatsAppSettings() {
    // Get settings from localStorage or use defaults
    const storedSettings = localStorage.getItem('whatsappSettings');

    if (storedSettings) {
        whatsappSettings = JSON.parse(storedSettings);
    } else {
        // Default settings
        whatsappSettings = {
            enabled: true,
            defaultCountryCode: '+965',
            templates: {
                debtNotification: 'مرحباً {parent_name}،\nنود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.\nنرجو سداد المبلغ في أقرب وقت ممكن.\nمع الشكر، إدارة المدرسة',
                paymentConfirmation: 'مرحباً {parent_name}،\nنشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.\nالرصيد المتبقي: {remaining_debt}\nمع الشكر، إدارة المدرسة',
                customMessage: 'مرحباً {parent_name}،\n{message}\nمع الشكر، إدارة المدرسة'
            }
        };

        // Save default settings
        localStorage.setItem('whatsappSettings', JSON.stringify(whatsappSettings));
    }

    // Populate form fields
    document.getElementById('enable-whatsapp').checked = whatsappSettings.enabled;
    document.getElementById('default-country-code').value = whatsappSettings.defaultCountryCode;
    document.getElementById('debt-notification-template').value = whatsappSettings.templates.debtNotification;
    document.getElementById('payment-confirmation-template').value = whatsappSettings.templates.paymentConfirmation;
    document.getElementById('custom-message-template').value = whatsappSettings.templates.customMessage;

    // Update test country code
    document.getElementById('test-country-code').textContent = whatsappSettings.defaultCountryCode;
}

// Load WhatsApp connection status
function loadWhatsAppConnectionStatus() {
    // Get connection status from localStorage or use default
    const storedConnection = localStorage.getItem('whatsappConnection');

    if (storedConnection) {
        whatsappConnection = JSON.parse(storedConnection);
    }

    updateConnectionStatusUI();
}

// Update connection status UI
function updateConnectionStatusUI() {
    const connectionStatus = document.getElementById('connection-status');
    const qrCodeContainer = document.getElementById('qr-code-container');
    const connectedInfo = document.getElementById('connected-info');
    const connectButton = document.getElementById('connect-whatsapp');
    const disconnectButton = document.getElementById('disconnect-whatsapp');

    // Hide all elements first
    connectionStatus.style.display = 'none';
    qrCodeContainer.style.display = 'none';
    connectedInfo.style.display = 'none';
    connectButton.style.display = 'none';
    disconnectButton.style.display = 'none';

    // Show elements based on connection status
    if (whatsappConnection.status === 'disconnected') {
        connectButton.style.display = 'inline-block';
    } else if (whatsappConnection.status === 'connecting') {
        qrCodeContainer.style.display = 'block';
        generateQRCode();
    } else if (whatsappConnection.status === 'connected') {
        connectedInfo.style.display = 'block';
        disconnectButton.style.display = 'inline-block';

        // Update connected info
        document.getElementById('connected-phone').textContent = whatsappConnection.phone;
        document.getElementById('connected-name').textContent = whatsappConnection.name;
        document.getElementById('connected-time').textContent = formatDateTime(whatsappConnection.connectedAt);
    }
}

// Generate QR code for WhatsApp Web
function generateQRCode() {
    // In a real implementation, this would be generated by the WhatsApp API
    // For demo purposes, we'll generate a dummy QR code

    const qrCode = qrcode(0, 'M');
    qrCode.addData('https://web.whatsapp.com/');
    qrCode.make();

    document.getElementById('qr-code').innerHTML = qrCode.createImgTag(5);
}

// Connect to WhatsApp
function connectToWhatsApp() {
    // In a real implementation, this would connect to the WhatsApp API
    // For demo purposes, we'll simulate the connection process

    whatsappConnection.status = 'connecting';
    updateConnectionStatusUI();

    // Simulate QR code scanning after 3 seconds
    setTimeout(function() {
        whatsappConnection.status = 'connected';
        whatsappConnection.phone = whatsappSettings.defaultCountryCode + ' 12345678';
        whatsappConnection.name = 'مدير النظام';
        whatsappConnection.connectedAt = new Date().toISOString();

        // Save connection status
        localStorage.setItem('whatsappConnection', JSON.stringify(whatsappConnection));

        // Update UI
        updateConnectionStatusUI();
    }, 3000);
}

// Disconnect from WhatsApp
function disconnectFromWhatsApp() {
    // In a real implementation, this would disconnect from the WhatsApp API
    // For demo purposes, we'll just update the status

    whatsappConnection.status = 'disconnected';
    whatsappConnection.phone = '';
    whatsappConnection.name = '';
    whatsappConnection.connectedAt = null;

    // Save connection status
    localStorage.setItem('whatsappConnection', JSON.stringify(whatsappConnection));

    // Update UI
    updateConnectionStatusUI();
}

// Save WhatsApp settings
function saveWhatsAppSettings() {
    whatsappSettings.enabled = document.getElementById('enable-whatsapp').checked;
    whatsappSettings.defaultCountryCode = document.getElementById('default-country-code').value;
    whatsappSettings.templates.debtNotification = document.getElementById('debt-notification-template').value;
    whatsappSettings.templates.paymentConfirmation = document.getElementById('payment-confirmation-template').value;
    whatsappSettings.templates.customMessage = document.getElementById('custom-message-template').value;

    // Save settings
    localStorage.setItem('whatsappSettings', JSON.stringify(whatsappSettings));

    // Update test country code
    document.getElementById('test-country-code').textContent = whatsappSettings.defaultCountryCode;

    // Show success message
    alert('تم حفظ إعدادات الواتساب بنجاح');
}

// Load classes for bulk messages
function loadClassesForBulkMessages() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const classes = [...new Set(students.map(student => student.class))].sort();

    const classSelect = document.getElementById('bulk-class');
    classSelect.innerHTML = '<option value="">اختر الصف الدراسي...</option>';

    classes.forEach(className => {
        const option = document.createElement('option');
        option.value = className;
        option.textContent = className;
        classSelect.appendChild(option);
    });
}

// Load students for custom selection
function loadStudentsForCustomSelection() {
    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Sort students by class and name
    students.sort((a, b) => {
        if (a.class === b.class) {
            return a.name.localeCompare(b.name);
        }
        return a.class.localeCompare(b.class);
    });

    const studentsSelect = document.getElementById('bulk-custom-students');
    studentsSelect.innerHTML = '';

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        studentsSelect.appendChild(option);
    });
}

// Load Twilio settings
function loadTwilioSettings() {
    // Populate form fields from TwilioIntegration object
    document.getElementById('twilio-account-sid').value = TwilioIntegration.config.accountSid || '';
    document.getElementById('twilio-auth-token').value = TwilioIntegration.config.authToken || '';
    document.getElementById('twilio-whatsapp-number').value = TwilioIntegration.config.whatsappNumber || '';

    // Setup Twilio form submission
    document.getElementById('twilio-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveTwilioSettings();
    });

    // Setup test connection button
    document.getElementById('test-twilio-connection').addEventListener('click', testTwilioConnection);
}

// Save Twilio settings
function saveTwilioSettings() {
    const config = {
        accountSid: document.getElementById('twilio-account-sid').value,
        authToken: document.getElementById('twilio-auth-token').value,
        whatsappNumber: document.getElementById('twilio-whatsapp-number').value
    };

    // Save using TwilioIntegration object
    TwilioIntegration.saveConfig(config);

    // Show success message
    alert('تم حفظ إعدادات Twilio بنجاح');
}

// Test Twilio connection
async function testTwilioConnection() {
    const statusContainer = document.getElementById('twilio-connection-status');
    statusContainer.innerHTML = `
        <div class="alert alert-info">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">جاري الاختبار...</span>
                </div>
                <div>جاري اختبار الاتصال بـ Twilio...</div>
            </div>
        </div>
    `;
    statusContainer.style.display = 'block';

    try {
        // Call the test function from TwilioIntegration object
        const result = await TwilioIntegration.testConnection();

        if (result.success) {
            statusContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${result.message}
                    <div class="mt-2 small">
                        <strong>Account SID:</strong> ${result.account.sid.substring(0, 8)}...${result.account.sid.substring(result.account.sid.length - 4)}<br>
                        <strong>Status:</strong> ${result.account.status}
                    </div>
                </div>
            `;
        } else {
            statusContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    ${result.message}
                </div>
            `;
        }
    } catch (error) {
        statusContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                حدث خطأ أثناء اختبار الاتصال: ${error.message}
            </div>
        `;
    }
}

// Load sent messages
function loadSentMessages() {
    const sentMessages = TwilioIntegration.getSentMessages();
    const tableBody = document.getElementById('sent-messages-table');

    if (sentMessages.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">لم يتم إرسال أي رسائل بعد</td>
            </tr>
        `;
        return;
    }

    // Sort messages by timestamp (newest first)
    sentMessages.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Generate table rows
    let html = '';
    sentMessages.forEach(message => {
        const date = new Date(message.timestamp);
        const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;

        // Truncate message if too long
        let messageText = message.message;
        if (messageText.length > 50) {
            messageText = messageText.substring(0, 50) + '...';
        }

        // Status badge
        let statusBadge = '';
        if (message.status === 'sent') {
            statusBadge = '<span class="badge bg-success">تم الإرسال</span>';
        } else if (message.status === 'delivered') {
            statusBadge = '<span class="badge bg-info">تم التسليم</span>';
        } else if (message.status === 'read') {
            statusBadge = '<span class="badge bg-primary">تمت القراءة</span>';
        } else if (message.status === 'failed') {
            statusBadge = '<span class="badge bg-danger">فشل</span>';
        } else {
            statusBadge = '<span class="badge bg-secondary">قيد الانتظار</span>';
        }

        html += `
            <tr>
                <td>${formattedDate}</td>
                <td>${message.to}</td>
                <td title="${message.message}">${messageText}</td>
                <td>${statusBadge}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;

    // Setup refresh and clear buttons
    document.getElementById('refresh-messages').addEventListener('click', loadSentMessages);
    document.getElementById('clear-messages').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من أنك تريد مسح سجل الرسائل المرسلة؟')) {
            TwilioIntegration.clearSentMessages();
            loadSentMessages();
        }
    });
}

// Setup event listeners
function setupEventListeners() {
    // WhatsApp settings form
    document.getElementById('whatsapp-settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveWhatsAppSettings();
    });

    // Connect/disconnect buttons
    document.getElementById('connect-whatsapp').addEventListener('click', connectToWhatsApp);
    document.getElementById('disconnect-whatsapp').addEventListener('click', disconnectFromWhatsApp);
    document.getElementById('refresh-status').addEventListener('click', updateConnectionStatusUI);

    // Test message form
    document.getElementById('test-message-form').addEventListener('submit', function(e) {
        e.preventDefault();
        sendTestMessage();
    });

    // Test message type change
    document.getElementById('test-message-type').addEventListener('change', function() {
        const customMessageContainer = document.getElementById('test-custom-message-container');
        if (this.value === 'custom') {
            customMessageContainer.style.display = 'block';
            document.getElementById('test-custom-message').setAttribute('required', 'required');
        } else {
            customMessageContainer.style.display = 'none';
            document.getElementById('test-custom-message').removeAttribute('required');
        }
    });

    // Bulk message form
    document.getElementById('bulk-message-form').addEventListener('submit', function(e) {
        e.preventDefault();
        showBulkMessagePreview();
    });

    // Bulk recipients change
    document.getElementById('bulk-recipients').addEventListener('change', function() {
        const classContainer = document.getElementById('bulk-class-container');
        const customContainer = document.getElementById('bulk-custom-container');

        classContainer.style.display = 'none';
        customContainer.style.display = 'none';

        if (this.value === 'class') {
            classContainer.style.display = 'block';
            document.getElementById('bulk-class').setAttribute('required', 'required');
        } else if (this.value === 'custom') {
            customContainer.style.display = 'block';
            document.getElementById('bulk-custom-students').setAttribute('required', 'required');
        } else {
            document.getElementById('bulk-class').removeAttribute('required');
            document.getElementById('bulk-custom-students').removeAttribute('required');
        }
    });

    // Bulk message type change
    document.getElementById('bulk-message-type').addEventListener('change', function() {
        const customMessageContainer = document.getElementById('bulk-custom-message-container');
        if (this.value === 'custom') {
            customMessageContainer.style.display = 'block';
            document.getElementById('bulk-custom-message').setAttribute('required', 'required');
        } else {
            customMessageContainer.style.display = 'none';
            document.getElementById('bulk-custom-message').removeAttribute('required');
        }
    });

    // Confirm send button
    document.getElementById('confirm-send-btn').addEventListener('click', function() {
        // Hide preview modal
        const previewModal = bootstrap.Modal.getInstance(document.getElementById('messagePreviewModal'));
        previewModal.hide();

        // Show sending progress modal
        const progressModal = new bootstrap.Modal(document.getElementById('sendingProgressModal'));
        progressModal.show();

        // Send bulk messages
        sendBulkMessages();
    });

    // Close progress button
    document.getElementById('close-progress-btn').addEventListener('click', function() {
        const progressModal = bootstrap.Modal.getInstance(document.getElementById('sendingProgressModal'));
        progressModal.hide();
    });
}

// Send test message
function sendTestMessage() {
    // Check if WhatsApp is connected
    if (whatsappConnection.status !== 'connected') {
        alert('يرجى الاتصال بالواتساب أولاً');
        return;
    }

    // Get form values
    const phone = document.getElementById('test-phone').value;
    const messageType = document.getElementById('test-message-type').value;
    const customMessage = document.getElementById('test-custom-message').value;

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    // Get message text
    let messageText = '';

    if (messageType === 'debt') {
        messageText = whatsappSettings.templates.debtNotification
            .replace('{parent_name}', 'ولي الأمر')
            .replace('{student_name}', 'الطالب التجريبي')
            .replace('{student_class}', 'الصف التجريبي')
            .replace('{debt_amount}', '10.000 د.ك')
            .replace('{school_name}', 'المدرسة النموذجية');
    } else if (messageType === 'payment') {
        messageText = whatsappSettings.templates.paymentConfirmation
            .replace('{parent_name}', 'ولي الأمر')
            .replace('{student_name}', 'الطالب التجريبي')
            .replace('{payment_amount}', '5.000 د.ك')
            .replace('{payment_date}', formatDate(new Date()))
            .replace('{remaining_debt}', '5.000 د.ك');
    } else if (messageType === 'custom') {
        messageText = whatsappSettings.templates.customMessage
            .replace('{parent_name}', 'ولي الأمر')
            .replace('{student_name}', 'الطالب التجريبي')
            .replace('{message}', customMessage)
            .replace('{school_name}', 'المدرسة النموذجية');
    }

    // In a real implementation, this would send the message via the WhatsApp API
    // For demo purposes, we'll just show a success message
    alert(`تم إرسال الرسالة التجريبية بنجاح إلى الرقم ${formattedPhone}`);

    // Reset form
    document.getElementById('test-message-form').reset();
    document.getElementById('test-custom-message-container').style.display = 'none';
}

// Show bulk message preview
function showBulkMessagePreview() {
    // Check if WhatsApp is connected
    if (whatsappConnection.status !== 'connected') {
        alert('يرجى الاتصال بالواتساب أولاً');
        return;
    }

    // Get form values
    const recipientsType = document.getElementById('bulk-recipients').value;
    const selectedClass = document.getElementById('bulk-class').value;
    const selectedStudents = Array.from(document.getElementById('bulk-custom-students').selectedOptions).map(option => parseInt(option.value));
    const messageType = document.getElementById('bulk-message-type').value;
    const customMessage = document.getElementById('bulk-custom-message').value;

    // Get recipients
    const students = JSON.parse(localStorage.getItem('students')) || [];
    let recipients = [];

    if (recipientsType === 'all') {
        recipients = students;
    } else if (recipientsType === 'with_debt') {
        // Get students with debt
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

        // Calculate debt for each student
        const studentDebts = {};

        transactions.forEach(transaction => {
            const studentId = transaction.student_id;
            const amount = transaction.type === 'purchase' ? transaction.amount : -transaction.amount;

            if (!studentDebts[studentId]) {
                studentDebts[studentId] = 0;
            }

            studentDebts[studentId] += amount;
        });

        // Filter students with debt
        recipients = students.filter(student => studentDebts[student.id] && studentDebts[student.id] > 0);
    } else if (recipientsType === 'class') {
        recipients = students.filter(student => student.class === selectedClass);
    } else if (recipientsType === 'custom') {
        recipients = students.filter(student => selectedStudents.includes(student.id));
    }

    // Check if there are recipients
    if (recipients.length === 0) {
        alert('لا يوجد مستلمون مطابقون للمعايير المحددة');
        return;
    }

    // Get message text for preview
    let messageText = '';

    if (messageType === 'debt') {
        messageText = whatsappSettings.templates.debtNotification
            .replace('{parent_name}', 'ولي الأمر')
            .replace('{student_name}', 'اسم الطالب')
            .replace('{student_class}', 'الصف الدراسي')
            .replace('{debt_amount}', 'قيمة الدين')
            .replace('{school_name}', 'المدرسة النموذجية');
    } else if (messageType === 'custom') {
        messageText = whatsappSettings.templates.customMessage
            .replace('{parent_name}', 'ولي الأمر')
            .replace('{student_name}', 'اسم الطالب')
            .replace('{message}', customMessage)
            .replace('{school_name}', 'المدرسة النموذجية');
    }

    // Update preview modal
    document.getElementById('preview-recipients').textContent = `${recipients.length} مستلم`;
    document.getElementById('preview-message').textContent = messageText;

    // Store data for sending
    sessionStorage.setItem('bulkMessageData', JSON.stringify({
        recipients: recipients.map(student => student.id),
        messageType,
        customMessage
    }));

    // Show preview modal
    const previewModal = new bootstrap.Modal(document.getElementById('messagePreviewModal'));
    previewModal.show();
}

// Send bulk messages
function sendBulkMessages() {
    // Get bulk message data
    const bulkMessageData = JSON.parse(sessionStorage.getItem('bulkMessageData'));
    const recipientIds = bulkMessageData.recipients;
    const messageType = bulkMessageData.messageType;
    const customMessage = bulkMessageData.customMessage;

    // Get students
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const recipients = students.filter(student => recipientIds.includes(student.id));

    // Get transactions for debt calculation
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Calculate debt for each student
    const studentDebts = {};

    transactions.forEach(transaction => {
        const studentId = transaction.student_id;
        const amount = transaction.type === 'purchase' ? transaction.amount : -transaction.amount;

        if (!studentDebts[studentId]) {
            studentDebts[studentId] = 0;
        }

        studentDebts[studentId] += amount;
    });

    // Update progress modal
    document.getElementById('sending-progress-bar').style.width = '0%';
    document.getElementById('sending-count').textContent = '0';
    document.getElementById('total-count').textContent = recipients.length;
    document.getElementById('sending-result').innerHTML = '';
    document.getElementById('close-progress-btn').style.display = 'none';

    // Send messages
    let successCount = 0;
    let failCount = 0;
    let currentIndex = 0;

    const sendInterval = setInterval(function() {
        if (currentIndex >= recipients.length) {
            // All messages sent
            clearInterval(sendInterval);

            // Update progress
            document.getElementById('sending-progress-bar').style.width = '100%';
            document.getElementById('sending-status').textContent = 'اكتمل الإرسال';

            // Show result
            document.getElementById('sending-result').innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    تم إرسال ${successCount} رسالة بنجاح
                </div>
                ${failCount > 0 ? `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    فشل إرسال ${failCount} رسالة
                </div>
                ` : ''}
            `;

            // Show close button
            document.getElementById('close-progress-btn').style.display = 'block';

            return;
        }

        const student = recipients[currentIndex];
        const debtAmount = studentDebts[student.id] || 0;

        // Get message text
        let messageText = '';

        if (messageType === 'debt') {
            messageText = whatsappSettings.templates.debtNotification
                .replace('{parent_name}', student.parent_name)
                .replace('{student_name}', student.name)
                .replace('{student_class}', student.class)
                .replace('{debt_amount}', formatKuwaitiCurrency(debtAmount))
                .replace('{school_name}', 'المدرسة النموذجية');
        } else if (messageType === 'custom') {
            messageText = whatsappSettings.templates.customMessage
                .replace('{parent_name}', student.parent_name)
                .replace('{student_name}', student.name)
                .replace('{message}', customMessage)
                .replace('{school_name}', 'المدرسة النموذجية');
        }

        // In a real implementation, this would send the message via the WhatsApp API
        // For demo purposes, we'll simulate success/failure

        // Simulate 90% success rate
        const isSuccess = Math.random() < 0.9;

        if (isSuccess) {
            successCount++;
        } else {
            failCount++;
        }

        // Update progress
        currentIndex++;
        const progress = Math.round((currentIndex / recipients.length) * 100);
        document.getElementById('sending-progress-bar').style.width = `${progress}%`;
        document.getElementById('sending-count').textContent = currentIndex;

    }, 500); // Send a message every 500ms
}

// Format phone number
function formatPhoneNumber(phone) {
    // Remove any non-digit characters
    phone = phone.replace(/\D/g, '');

    // Add country code if not present
    if (!phone.startsWith('+')) {
        phone = whatsappSettings.defaultCountryCode + phone;
    }

    return phone;
}

// Format date
function formatDate(date) {
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

// Format date and time
function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
}

// Format Kuwaiti currency
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // Format with 3 decimal places
    const formatted = amount.toFixed(3);

    // Add currency symbol
    return `${formatted} د.ك`;
}
