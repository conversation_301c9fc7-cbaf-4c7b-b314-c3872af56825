// Print Report Functionality

/**
 * Print a specific report or table
 * @param {string} title - Report title
 * @param {string} subtitle - Report subtitle
 * @param {HTMLElement} content - The content to print
 * @param {Object} options - Additional options
 */
function printReport(title, subtitle, content, options = {}) {
    // Default options
    const defaultOptions = {
        showDate: true,
        showLogo: true,
        showFooter: true,
        additionalInfo: null,
        orientation: 'portrait' // 'portrait' or 'landscape'
    };

    // Merge options
    const printOptions = { ...defaultOptions, ...options };

    // Create a new window
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Get current date
    const now = new Date();
    const formattedDate = `${now.getDate()}/${now.getMonth() + 1}/${now.getFullYear()}`;

    // Create HTML content
    let htmlContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                /* Print Report Styles */
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    direction: rtl;
                }

                .report-header {
                    text-align: center;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #000;
                }

                .report-header h1 {
                    margin: 0;
                    font-size: 24px;
                    margin-bottom: 5px;
                }

                .report-header h2 {
                    margin: 0;
                    font-size: 18px;
                    font-weight: normal;
                }

                .report-header .logo {
                    max-width: 100px;
                    margin-bottom: 10px;
                }

                .report-info {
                    margin-bottom: 20px;
                }

                .report-info table {
                    width: 100%;
                    border-collapse: collapse;
                }

                .report-info table td {
                    padding: 5px;
                }

                .report-info table td:first-child {
                    font-weight: bold;
                    width: 150px;
                }

                .report-content {
                    margin-bottom: 20px;
                }

                .report-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }

                .report-table th,
                .report-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }

                .report-table th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }

                .report-table tr:nth-child(even) {
                    background-color: #f9f9f9;
                }

                .report-summary {
                    margin-top: 20px;
                    border-top: 1px solid #ddd;
                    padding-top: 10px;
                }

                .report-summary table {
                    width: 300px;
                    margin-right: auto;
                    border-collapse: collapse;
                }

                .report-summary table td {
                    padding: 5px;
                }

                .report-summary table td:first-child {
                    font-weight: bold;
                }

                .report-footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 10px;
                }

                .text-danger {
                    color: #dc3545;
                }

                .text-success {
                    color: #28a745;
                }

                .badge {
                    display: inline-block;
                    padding: 3px 7px;
                    font-size: 12px;
                    font-weight: bold;
                    line-height: 1;
                    text-align: center;
                    white-space: nowrap;
                    vertical-align: baseline;
                    border-radius: 10px;
                }

                .badge-danger {
                    background-color: #dc3545;
                    color: white;
                }

                .badge-success {
                    background-color: #28a745;
                    color: white;
                }

                .badge-warning {
                    background-color: #ffc107;
                    color: black;
                }

                .receipt {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    border: 1px solid #ddd;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }

                .receipt-header {
                    text-align: center;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #000;
                }

                .receipt-title {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }

                .receipt-subtitle {
                    font-size: 16px;
                    margin-bottom: 5px;
                }

                .receipt-number {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }

                .receipt-date {
                    font-size: 14px;
                    margin-bottom: 5px;
                }

                .receipt-details {
                    margin-bottom: 20px;
                }

                .receipt-details table {
                    width: 100%;
                    border-collapse: collapse;
                }

                .receipt-details td {
                    padding: 5px;
                }

                .receipt-details td:first-child {
                    font-weight: bold;
                    width: 150px;
                }

                .receipt-amount {
                    margin: 20px 0;
                    padding: 10px;
                    border: 1px solid #ddd;
                    background-color: #f9f9f9;
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                }

                .receipt-notes {
                    margin-top: 20px;
                    padding: 10px;
                    border-top: 1px dashed #ddd;
                }

                .receipt-footer {
                    margin-top: 30px;
                    display: flex;
                    justify-content: space-between;
                }

                .receipt-signature {
                    width: 45%;
                    text-align: center;
                }

                .signature-line {
                    margin-top: 50px;
                    border-top: 1px solid #000;
                    padding-top: 5px;
                }

                @media print {
                    body {
                        padding: 0;
                        margin: 0;
                    }

                    @page {
                        margin: 1.5cm;
                    }
                }
            </style>
            <style>
                @page {
                    size: ${printOptions.orientation};
                }
            </style>
        </head>
        <body>
            <div class="report-header">
                ${printOptions.showLogo ? '<img src="img/logo.png" alt="Logo" class="logo">' : ''}
                <h1>${title}</h1>
                <h2>${subtitle}</h2>
                ${printOptions.showDate ? `<p>تاريخ التقرير: ${formattedDate}</p>` : ''}
            </div>
    `;

    // Add additional info if provided
    if (printOptions.additionalInfo) {
        htmlContent += `
            <div class="report-info">
                <table>
        `;

        for (const [key, value] of Object.entries(printOptions.additionalInfo)) {
            htmlContent += `
                    <tr>
                        <td>${key}:</td>
                        <td>${value}</td>
                    </tr>
            `;
        }

        htmlContent += `
                </table>
            </div>
        `;
    }

    // Add content
    htmlContent += `
            <div class="report-content">
                ${content.outerHTML || content}
            </div>
    `;

    // Add footer
    if (printOptions.showFooter) {
        htmlContent += `
            <div class="report-footer">
                <p>© ${new Date().getFullYear()} نظام إدارة ديون المقصف المدرسي</p>
            </div>
        `;
    }

    // Close HTML
    htmlContent += `
        </body>
        </html>
    `;

    // Write to the new window
    printWindow.document.open();
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Add event listener for when resources are loaded
    printWindow.onload = function() {
        // Delay printing to ensure styles are applied
        setTimeout(function() {
            printWindow.print();
            // Close the window after printing (optional)
            // printWindow.close();
        }, 500);
    };
}

/**
 * Print student details
 * @param {Object} student - Student object
 * @param {Array} transactions - Transactions array
 */
function printStudentDetails(student, transactions) {
    // Create a div to hold the content
    const content = document.createElement('div');

    // Create student info table
    const studentInfoTable = document.createElement('table');
    studentInfoTable.className = 'report-table';
    studentInfoTable.innerHTML = `
        <tr>
            <th colspan="2">معلومات الطالب</th>
        </tr>
        <tr>
            <td>اسم الطالب:</td>
            <td>${student.name}</td>
        </tr>
        <tr>
            <td>رقم الهوية:</td>
            <td>${student.student_id}</td>
        </tr>
        <tr>
            <td>الصف:</td>
            <td>${student.class}</td>
        </tr>
        <tr>
            <td>اسم ولي الأمر:</td>
            <td>${student.parent_name}</td>
        </tr>
        <tr>
            <td>رقم الجوال:</td>
            <td>${student.phone_number}</td>
        </tr>
        ${student.email ? `
        <tr>
            <td>البريد الإلكتروني:</td>
            <td>${student.email}</td>
        </tr>
        ` : ''}
        ${student.address ? `
        <tr>
            <td>العنوان:</td>
            <td>${student.address}</td>
        </tr>
        ` : ''}
    `;
    content.appendChild(studentInfoTable);

    // Calculate student debt
    let totalPurchases = 0;
    let totalPayments = 0;

    transactions.forEach(transaction => {
        if (transaction.student_id == student.id) {
            if (transaction.type === 'purchase') {
                totalPurchases += parseFloat(transaction.amount);
            } else if (transaction.type === 'payment') {
                totalPayments += parseFloat(transaction.amount);
            }
        }
    });

    const debt = totalPurchases - totalPayments;

    // Create debt summary
    const debtSummary = document.createElement('div');
    debtSummary.className = 'report-summary';
    debtSummary.innerHTML = `
        <table>
            <tr>
                <td>إجمالي المشتريات:</td>
                <td>${formatKuwaitiCurrency(totalPurchases)}</td>
            </tr>
            <tr>
                <td>إجمالي المدفوعات:</td>
                <td>${formatKuwaitiCurrency(totalPayments)}</td>
            </tr>
            <tr>
                <td>الرصيد الحالي:</td>
                <td class="${debt > 0 ? 'text-danger' : 'text-success'}">
                    ${debt > 0
                        ? formatKuwaitiCurrency(debt) + ' (دين)'
                        : debt < 0
                            ? formatKuwaitiCurrency(Math.abs(debt)) + ' (رصيد)'
                            : '0 فلس (متعادل)'}
                </td>
            </tr>
        </table>
    `;
    content.appendChild(debtSummary);

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == student.id);

    // Create transactions table if there are any
    if (studentTransactions.length > 0) {
        const transactionsTable = document.createElement('table');
        transactionsTable.className = 'report-table';

        // Create table header
        let tableHTML = `
            <tr>
                <th colspan="5">سجل المعاملات</th>
            </tr>
            <tr>
                <th>التاريخ</th>
                <th>النوع</th>
                <th>المبلغ</th>
                <th>الوصف</th>
                <th>الرصيد</th>
            </tr>
        `;

        // Sort transactions by date
        studentTransactions.sort((a, b) => new Date(a.date) - new Date(b.date));

        // Calculate running balance
        let runningBalance = 0;

        // Add transactions
        studentTransactions.forEach(transaction => {
            const amount = parseFloat(transaction.amount);

            if (transaction.type === 'purchase') {
                runningBalance += amount;
            } else if (transaction.type === 'payment') {
                runningBalance -= amount;
            }

            tableHTML += `
                <tr>
                    <td>${formatDate(transaction.date)}</td>
                    <td>
                        ${transaction.type === 'purchase'
                            ? '<span class="badge badge-danger">مشتريات</span>'
                            : '<span class="badge badge-success">دفعة</span>'}
                    </td>
                    <td>${formatKuwaitiCurrency(amount)}</td>
                    <td>${transaction.description}</td>
                    <td class="${runningBalance > 0 ? 'text-danger' : 'text-success'}">
                        ${runningBalance > 0
                            ? formatKuwaitiCurrency(runningBalance) + ' (دين)'
                            : runningBalance < 0
                                ? formatKuwaitiCurrency(Math.abs(runningBalance)) + ' (رصيد)'
                                : '0 فلس (متعادل)'}
                    </td>
                </tr>
            `;
        });

        transactionsTable.innerHTML = tableHTML;
        content.appendChild(transactionsTable);
    }

    // Print the report
    printReport(
        'تقرير تفاصيل الطالب',
        'نظام إدارة ديون المقصف المدرسي',
        content,
        {
            additionalInfo: {
                'تاريخ الطباعة': formatDate(new Date().toISOString())
            }
        }
    );
}

/**
 * Print transactions table
 * @param {Array} transactions - Transactions array
 * @param {Array} students - Students array
 * @param {Object} filters - Filter options
 */
function printTransactionsTable(transactions, students, filters = {}) {
    // Apply filters
    let filteredTransactions = [...transactions];

    if (filters.studentId) {
        filteredTransactions = filteredTransactions.filter(t => t.student_id == filters.studentId);
    }

    if (filters.type) {
        filteredTransactions = filteredTransactions.filter(t => t.type === filters.type);
    }

    if (filters.dateFrom) {
        const fromDate = new Date(filters.dateFrom);
        filteredTransactions = filteredTransactions.filter(t => new Date(t.date) >= fromDate);
    }

    if (filters.dateTo) {
        const toDate = new Date(filters.dateTo);
        toDate.setHours(23, 59, 59, 999); // End of day
        filteredTransactions = filteredTransactions.filter(t => new Date(t.date) <= toDate);
    }

    // Sort transactions by date (newest first)
    filteredTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Create a table
    const table = document.createElement('table');
    table.className = 'report-table';

    // Create table header
    let tableHTML = `
        <tr>
            <th>التاريخ</th>
            <th>الطالب</th>
            <th>الصف</th>
            <th>النوع</th>
            <th>المبلغ</th>
            <th>الوصف</th>
        </tr>
    `;

    // Calculate totals
    let totalPurchases = 0;
    let totalPayments = 0;

    // Add transactions
    filteredTransactions.forEach(transaction => {
        const student = students.find(s => s.id == transaction.student_id) || { name: 'غير معروف', class: '-' };
        const amount = parseFloat(transaction.amount);

        if (transaction.type === 'purchase') {
            totalPurchases += amount;
        } else if (transaction.type === 'payment') {
            totalPayments += amount;
        }

        tableHTML += `
            <tr>
                <td>${formatDate(transaction.date)}</td>
                <td>${student.name}</td>
                <td>${student.class}</td>
                <td>
                    ${transaction.type === 'purchase'
                        ? '<span class="badge badge-danger">مشتريات</span>'
                        : '<span class="badge badge-success">دفعة</span>'}
                </td>
                <td>${formatKuwaitiCurrency(amount)}</td>
                <td>${transaction.description}</td>
            </tr>
        `;
    });

    table.innerHTML = tableHTML;

    // Create summary
    const summary = document.createElement('div');
    summary.className = 'report-summary';
    summary.innerHTML = `
        <table>
            <tr>
                <td>عدد المعاملات:</td>
                <td>${filteredTransactions.length}</td>
            </tr>
            <tr>
                <td>إجمالي المشتريات:</td>
                <td>${formatKuwaitiCurrency(totalPurchases)}</td>
            </tr>
            <tr>
                <td>إجمالي المدفوعات:</td>
                <td>${formatKuwaitiCurrency(totalPayments)}</td>
            </tr>
            <tr>
                <td>الصافي:</td>
                <td class="${totalPurchases > totalPayments ? 'text-danger' : 'text-success'}">
                    ${formatKuwaitiCurrency(totalPurchases - totalPayments)}
                </td>
            </tr>
        </table>
    `;

    // Create content div
    const content = document.createElement('div');
    content.appendChild(table);
    content.appendChild(summary);

    // Create subtitle based on filters
    let subtitle = 'سجل المعاملات';

    if (filters.studentId) {
        const student = students.find(s => s.id == filters.studentId);
        if (student) {
            subtitle += ` - ${student.name}`;
        }
    }

    if (filters.type) {
        subtitle += ` - ${filters.type === 'purchase' ? 'المشتريات' : 'المدفوعات'}`;
    }

    if (filters.dateFrom || filters.dateTo) {
        subtitle += ' - الفترة: ';
        if (filters.dateFrom) {
            subtitle += formatDate(filters.dateFrom);
        } else {
            subtitle += 'البداية';
        }
        subtitle += ' إلى ';
        if (filters.dateTo) {
            subtitle += formatDate(filters.dateTo);
        } else {
            subtitle += 'النهاية';
        }
    }

    // Print the report
    printReport(
        'تقرير المعاملات',
        subtitle,
        content,
        {
            orientation: 'landscape'
        }
    );
}

/**
 * Print students list
 * @param {Array} students - Students array
 * @param {Object} studentDebts - Student debts object
 * @param {Object} filters - Filter options
 */
function printStudentsList(students, studentDebts, filters = {}) {
    // Apply filters
    let filteredStudents = [...students];

    if (filters.class) {
        filteredStudents = filteredStudents.filter(s => s.class === filters.class);
    }

    if (filters.debtFilter) {
        if (filters.debtFilter === 'debt') {
            filteredStudents = filteredStudents.filter(s => (studentDebts[s.id]?.debt || 0) > 0);
        } else if (filters.debtFilter === 'no-debt') {
            filteredStudents = filteredStudents.filter(s => (studentDebts[s.id]?.debt || 0) <= 0);
        }
    }

    if (filters.debtMin !== undefined) {
        const minDebt = parseFloat(filters.debtMin) / 1000; // Convert from fils to KWD
        filteredStudents = filteredStudents.filter(s => (studentDebts[s.id]?.debt || 0) >= minDebt);
    }

    if (filters.debtMax !== undefined && filters.debtMax !== Infinity) {
        const maxDebt = parseFloat(filters.debtMax) / 1000; // Convert from fils to KWD
        filteredStudents = filteredStudents.filter(s => (studentDebts[s.id]?.debt || 0) <= maxDebt);
    }

    // Sort students by name
    filteredStudents.sort((a, b) => a.name.localeCompare(b.name));

    // Create a table
    const table = document.createElement('table');
    table.className = 'report-table';

    // Create table header
    let tableHTML = `
        <tr>
            <th>#</th>
            <th>اسم الطالب</th>
            <th>الصف</th>
            <th>رقم الهوية</th>
            <th>اسم ولي الأمر</th>
            <th>رقم الجوال</th>
            <th>الدين</th>
        </tr>
    `;

    // Calculate total debt
    let totalDebt = 0;
    let studentsWithDebt = 0;

    // Add students
    filteredStudents.forEach((student, index) => {
        const debt = studentDebts[student.id]?.debt || 0;

        if (debt > 0) {
            totalDebt += debt;
            studentsWithDebt++;
        }

        tableHTML += `
            <tr>
                <td>${index + 1}</td>
                <td>${student.name}</td>
                <td>${student.class}</td>
                <td>${student.student_id}</td>
                <td>${student.parent_name}</td>
                <td>${student.phone_number}</td>
                <td class="${debt > 0 ? 'text-danger' : debt < 0 ? 'text-success' : ''}">
                    ${debt > 0
                        ? formatKuwaitiCurrency(debt)
                        : debt < 0
                            ? formatKuwaitiCurrency(Math.abs(debt)) + ' (رصيد)'
                            : '0 فلس'}
                </td>
            </tr>
        `;
    });

    table.innerHTML = tableHTML;

    // Create summary
    const summary = document.createElement('div');
    summary.className = 'report-summary';
    summary.innerHTML = `
        <table>
            <tr>
                <td>عدد الطلاب:</td>
                <td>${filteredStudents.length}</td>
            </tr>
            <tr>
                <td>عدد الطلاب المدينين:</td>
                <td>${studentsWithDebt}</td>
            </tr>
            <tr>
                <td>إجمالي الديون:</td>
                <td class="text-danger">${formatKuwaitiCurrency(totalDebt)}</td>
            </tr>
        </table>
    `;

    // Create content div
    const content = document.createElement('div');
    content.appendChild(table);
    content.appendChild(summary);

    // Create subtitle based on filters
    let subtitle = 'قائمة الطلاب';

    if (filters.class) {
        subtitle += ` - ${filters.class}`;
    }

    if (filters.debtFilter) {
        if (filters.debtFilter === 'debt') {
            subtitle += ' - المدينين فقط';
        } else if (filters.debtFilter === 'no-debt') {
            subtitle += ' - غير المدينين فقط';
        }
    }

    // Print the report
    printReport(
        'تقرير الطلاب',
        subtitle,
        content,
        {
            orientation: 'landscape'
        }
    );
}

/**
 * Print installment plan details
 * @param {Object} plan - Installment plan object
 * @param {Array} students - Students array
 */
function printInstallmentPlan(plan, students) {
    // Create a div to hold the content
    const content = document.createElement('div');

    // Create plan info table
    const planInfoTable = document.createElement('table');
    planInfoTable.className = 'report-table';
    planInfoTable.innerHTML = `
        <tr>
            <th colspan="2">معلومات خطة الأقساط</th>
        </tr>
        <tr>
            <td>الطالب:</td>
            <td>${plan.student_name}</td>
        </tr>
        <tr>
            <td>الصف:</td>
            <td>${plan.student_class}</td>
        </tr>
        <tr>
            <td>المبلغ الإجمالي:</td>
            <td>${formatKuwaitiCurrency(plan.total_amount)}</td>
        </tr>
        <tr>
            <td>عدد الأقساط:</td>
            <td>${plan.installment_count}</td>
        </tr>
        <tr>
            <td>قيمة القسط:</td>
            <td>${formatKuwaitiCurrency(plan.total_amount / plan.installment_count)}</td>
        </tr>
        <tr>
            <td>تاريخ البدء:</td>
            <td>${formatDate(plan.start_date)}</td>
        </tr>
        <tr>
            <td>تاريخ الانتهاء المتوقع:</td>
            <td>${formatDate(plan.end_date)}</td>
        </tr>
        <tr>
            <td>الحالة:</td>
            <td>${plan.status === 'active' ? 'نشطة' : 'مكتملة'}</td>
        </tr>
        ${plan.notes ? `
        <tr>
            <td>ملاحظات:</td>
            <td>${plan.notes}</td>
        </tr>
        ` : ''}
    `;
    content.appendChild(planInfoTable);

    // Calculate paid and remaining amounts
    const paidInstallments = plan.installments.filter(installment => installment.status === 'paid');
    const paidAmount = paidInstallments.reduce((sum, installment) => sum + installment.amount, 0);
    const remainingAmount = plan.total_amount - paidAmount;

    // Create payment summary
    const paymentSummary = document.createElement('div');
    paymentSummary.className = 'report-summary';
    paymentSummary.innerHTML = `
        <table>
            <tr>
                <td>المبلغ المدفوع:</td>
                <td>${formatKuwaitiCurrency(paidAmount)} (${paidInstallments.length}/${plan.installment_count})</td>
            </tr>
            <tr>
                <td>المبلغ المتبقي:</td>
                <td>${formatKuwaitiCurrency(remainingAmount)}</td>
            </tr>
        </table>
    `;
    content.appendChild(paymentSummary);

    // Create installments table
    const installmentsTable = document.createElement('table');
    installmentsTable.className = 'report-table';

    // Create table header
    let tableHTML = `
        <tr>
            <th colspan="6">تفاصيل الأقساط</th>
        </tr>
        <tr>
            <th>رقم القسط</th>
            <th>المبلغ</th>
            <th>تاريخ الاستحقاق</th>
            <th>الحالة</th>
            <th>تاريخ الدفع</th>
            <th>طريقة الدفع</th>
        </tr>
    `;

    // Add installments
    plan.installments.forEach(installment => {
        tableHTML += `
            <tr>
                <td>${installment.index}</td>
                <td>${formatKuwaitiCurrency(installment.amount)}</td>
                <td>${formatDate(installment.due_date)}</td>
                <td>
                    ${installment.status === 'paid'
                        ? '<span class="badge badge-success">مدفوع</span>'
                        : isOverdue(installment.due_date)
                            ? '<span class="badge badge-danger">متأخر</span>'
                            : '<span class="badge badge-warning">قيد الانتظار</span>'}
                </td>
                <td>${installment.payment_date ? formatDate(installment.payment_date) : '-'}</td>
                <td>${getPaymentMethodText(installment.payment_method)}</td>
            </tr>
        `;
    });

    installmentsTable.innerHTML = tableHTML;
    content.appendChild(installmentsTable);

    // Print the report
    printReport(
        'تقرير خطة الأقساط',
        'نظام إدارة ديون المقصف المدرسي',
        content
    );
}

/**
 * Print payment receipt
 * @param {Object} payment - Payment object
 * @param {Object} student - Student object
 */
function printPaymentReceipt(payment, student) {
    // Create receipt content
    const content = document.createElement('div');
    content.className = 'receipt';

    // Create receipt header
    const header = document.createElement('div');
    header.className = 'receipt-header';
    header.innerHTML = `
        <div class="receipt-title">سند قبض</div>
        <div class="receipt-subtitle">نظام إدارة ديون المقصف المدرسي</div>
        <div class="receipt-number">رقم السند: ${payment.id}</div>
        <div class="receipt-date">التاريخ: ${formatDate(payment.date)}</div>
    `;
    content.appendChild(header);

    // Create receipt details
    const details = document.createElement('div');
    details.className = 'receipt-details';
    details.innerHTML = `
        <table>
            <tr>
                <td>استلمنا من:</td>
                <td>${student.name}</td>
            </tr>
            <tr>
                <td>الصف:</td>
                <td>${student.class}</td>
            </tr>
            <tr>
                <td>رقم الهوية:</td>
                <td>${student.student_id}</td>
            </tr>
            <tr>
                <td>ولي الأمر:</td>
                <td>${student.parent_name}</td>
            </tr>
        </table>
    `;
    content.appendChild(details);

    // Create amount section
    const amount = document.createElement('div');
    amount.className = 'receipt-amount';
    amount.innerHTML = `
        <div>مبلغ وقدره</div>
        <div style="font-size: 24px; margin: 10px 0;">${formatKuwaitiCurrency(payment.amount)}</div>
        <div>${convertNumberToArabicWords(payment.amount)}</div>
    `;
    content.appendChild(amount);

    // Create payment details
    const paymentDetails = document.createElement('div');
    paymentDetails.className = 'receipt-details';
    paymentDetails.innerHTML = `
        <table>
            <tr>
                <td>وذلك عن:</td>
                <td>${payment.description || 'دفعة من دين المقصف المدرسي'}</td>
            </tr>
            <tr>
                <td>طريقة الدفع:</td>
                <td>${getPaymentMethodText(payment.payment_method || 'cash')}</td>
            </tr>
        </table>
    `;
    content.appendChild(paymentDetails);

    // Create notes section if available
    if (payment.notes) {
        const notes = document.createElement('div');
        notes.className = 'receipt-notes';
        notes.innerHTML = `
            <div><strong>ملاحظات:</strong></div>
            <div>${payment.notes}</div>
        `;
        content.appendChild(notes);
    }

    // Create signature section
    const signature = document.createElement('div');
    signature.className = 'receipt-footer';
    signature.innerHTML = `
        <div class="receipt-signature">
            <div>المستلم</div>
            <div class="signature-line">التوقيع</div>
        </div>
        <div class="receipt-signature">
            <div>أمين الصندوق</div>
            <div class="signature-line">التوقيع</div>
        </div>
    `;
    content.appendChild(signature);

    // Print the receipt
    printReport(
        'سند قبض',
        'نظام إدارة ديون المقصف المدرسي',
        content,
        {
            showLogo: true,
            showDate: false,
            showFooter: false
        }
    );
}

/**
 * Print purchase invoice
 * @param {Object} purchase - Purchase object
 * @param {Object} student - Student object
 */
function printPurchaseInvoice(purchase, student) {
    // Create invoice content
    const content = document.createElement('div');
    content.className = 'receipt';

    // Create invoice header
    const header = document.createElement('div');
    header.className = 'receipt-header';
    header.innerHTML = `
        <div class="receipt-title">فاتورة مشتريات</div>
        <div class="receipt-subtitle">نظام إدارة ديون المقصف المدرسي</div>
        <div class="receipt-number">رقم الفاتورة: ${purchase.id}</div>
        <div class="receipt-date">التاريخ: ${formatDate(purchase.date)}</div>
    `;
    content.appendChild(header);

    // Create invoice details
    const details = document.createElement('div');
    details.className = 'receipt-details';
    details.innerHTML = `
        <table>
            <tr>
                <td>اسم الطالب:</td>
                <td>${student.name}</td>
            </tr>
            <tr>
                <td>الصف:</td>
                <td>${student.class}</td>
            </tr>
            <tr>
                <td>رقم الهوية:</td>
                <td>${student.student_id}</td>
            </tr>
        </table>
    `;
    content.appendChild(details);

    // Create amount section
    const amount = document.createElement('div');
    amount.className = 'receipt-amount';
    amount.innerHTML = `
        <div>مبلغ وقدره</div>
        <div style="font-size: 24px; margin: 10px 0;">${formatKuwaitiCurrency(purchase.amount)}</div>
        <div>${convertNumberToArabicWords(purchase.amount)}</div>
    `;
    content.appendChild(amount);

    // Create purchase details
    const purchaseDetails = document.createElement('div');
    purchaseDetails.className = 'receipt-details';
    purchaseDetails.innerHTML = `
        <table>
            <tr>
                <td>وصف المشتريات:</td>
                <td>${purchase.description || 'مشتريات من المقصف المدرسي'}</td>
            </tr>
            <tr>
                <td>حالة الدفع:</td>
                <td><span class="badge badge-warning">آجل</span></td>
            </tr>
        </table>
    `;
    content.appendChild(purchaseDetails);

    // Create notes section if available
    if (purchase.notes) {
        const notes = document.createElement('div');
        notes.className = 'receipt-notes';
        notes.innerHTML = `
            <div><strong>ملاحظات:</strong></div>
            <div>${purchase.notes}</div>
        `;
        content.appendChild(notes);
    }

    // Create signature section
    const signature = document.createElement('div');
    signature.className = 'receipt-footer';
    signature.innerHTML = `
        <div class="receipt-signature">
            <div>المستلم</div>
            <div class="signature-line">التوقيع</div>
        </div>
        <div class="receipt-signature">
            <div>البائع</div>
            <div class="signature-line">التوقيع</div>
        </div>
    `;
    content.appendChild(signature);

    // Print the invoice
    printReport(
        'فاتورة مشتريات',
        'نظام إدارة ديون المقصف المدرسي',
        content,
        {
            showLogo: true,
            showDate: false,
            showFooter: false
        }
    );
}

// Helper functions

// Format Kuwaiti currency
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return new Intl.NumberFormat('ar-KW', {
            style: 'currency',
            currency: 'KWD',
            minimumFractionDigits: 3
        }).format(amount);
    }
}

// Convert number to Arabic words
function convertNumberToArabicWords(number) {
    // Simple implementation for Kuwaiti Dinar
    const arabicNumbers = [
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة', 'عشرة',
        'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];

    const arabicTens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];

    const arabicHundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];

    const arabicThousands = ['', 'ألف', 'ألفان', 'ثلاثة آلاف', 'أربعة آلاف', 'خمسة آلاف', 'ستة آلاف', 'سبعة آلاف', 'ثمانية آلاف', 'تسعة آلاف'];

    // Convert to number and handle negative values
    number = Math.abs(parseFloat(number));

    // Round to 3 decimal places (Kuwaiti Dinar has 3 decimal places)
    number = Math.round(number * 1000) / 1000;

    // Split into integer and decimal parts
    const integerPart = Math.floor(number);
    const decimalPart = Math.round((number - integerPart) * 1000);

    // Convert integer part to words
    let result = '';

    if (integerPart === 0) {
        result = 'صفر';
    } else if (integerPart < 20) {
        result = arabicNumbers[integerPart];
    } else if (integerPart < 100) {
        const ones = integerPart % 10;
        const tens = Math.floor(integerPart / 10);

        if (ones === 0) {
            result = arabicTens[tens];
        } else {
            result = arabicNumbers[ones] + ' و' + arabicTens[tens];
        }
    } else if (integerPart < 1000) {
        const hundreds = Math.floor(integerPart / 100);
        const remainder = integerPart % 100;

        if (remainder === 0) {
            result = arabicHundreds[hundreds];
        } else if (remainder < 20) {
            result = arabicHundreds[hundreds] + ' و' + arabicNumbers[remainder];
        } else {
            const ones = remainder % 10;
            const tens = Math.floor(remainder / 10);

            if (ones === 0) {
                result = arabicHundreds[hundreds] + ' و' + arabicTens[tens];
            } else {
                result = arabicHundreds[hundreds] + ' و' + arabicNumbers[ones] + ' و' + arabicTens[tens];
            }
        }
    } else if (integerPart < 10000) {
        const thousands = Math.floor(integerPart / 1000);
        const remainder = integerPart % 1000;

        if (remainder === 0) {
            result = arabicThousands[thousands];
        } else {
            let remainderWords = '';

            if (remainder < 20) {
                remainderWords = arabicNumbers[remainder];
            } else if (remainder < 100) {
                const ones = remainder % 10;
                const tens = Math.floor(remainder / 10);

                if (ones === 0) {
                    remainderWords = arabicTens[tens];
                } else {
                    remainderWords = arabicNumbers[ones] + ' و' + arabicTens[tens];
                }
            } else {
                const hundreds = Math.floor(remainder / 100);
                const remainderOfHundreds = remainder % 100;

                if (remainderOfHundreds === 0) {
                    remainderWords = arabicHundreds[hundreds];
                } else if (remainderOfHundreds < 20) {
                    remainderWords = arabicHundreds[hundreds] + ' و' + arabicNumbers[remainderOfHundreds];
                } else {
                    const ones = remainderOfHundreds % 10;
                    const tens = Math.floor(remainderOfHundreds / 10);

                    if (ones === 0) {
                        remainderWords = arabicHundreds[hundreds] + ' و' + arabicTens[tens];
                    } else {
                        remainderWords = arabicHundreds[hundreds] + ' و' + arabicNumbers[ones] + ' و' + arabicTens[tens];
                    }
                }
            }

            result = arabicThousands[thousands] + ' و' + remainderWords;
        }
    } else {
        // For larger numbers, just return the number itself
        return number.toFixed(3) + ' دينار كويتي';
    }

    // Add currency name
    if (integerPart === 0) {
        result = '';
    } else if (integerPart === 1) {
        result += ' دينار كويتي';
    } else if (integerPart === 2) {
        result += ' ديناران كويتيان';
    } else if (integerPart >= 3 && integerPart <= 10) {
        result += ' دنانير كويتية';
    } else {
        result += ' دينار كويتي';
    }

    // Add decimal part if not zero
    if (decimalPart > 0) {
        let filsWords = '';

        if (decimalPart < 20) {
            filsWords = arabicNumbers[decimalPart];
        } else if (decimalPart < 100) {
            const ones = decimalPart % 10;
            const tens = Math.floor(decimalPart / 10);

            if (ones === 0) {
                filsWords = arabicTens[tens];
            } else {
                filsWords = arabicNumbers[ones] + ' و' + arabicTens[tens];
            }
        } else {
            const hundreds = Math.floor(decimalPart / 100);
            const remainder = decimalPart % 100;

            if (remainder === 0) {
                filsWords = arabicHundreds[hundreds];
            } else if (remainder < 20) {
                filsWords = arabicHundreds[hundreds] + ' و' + arabicNumbers[remainder];
            } else {
                const ones = remainder % 10;
                const tens = Math.floor(remainder / 10);

                if (ones === 0) {
                    filsWords = arabicHundreds[hundreds] + ' و' + arabicTens[tens];
                } else {
                    filsWords = arabicHundreds[hundreds] + ' و' + arabicNumbers[ones] + ' و' + arabicTens[tens];
                }
            }
        }

        // Add fils currency name
        if (decimalPart === 1) {
            filsWords += ' فلس';
        } else if (decimalPart === 2) {
            filsWords += ' فلسان';
        } else if (decimalPart >= 3 && decimalPart <= 10) {
            filsWords += ' فلوس';
        } else {
            filsWords += ' فلس';
        }

        if (integerPart > 0) {
            result += ' و' + filsWords;
        } else {
            result = filsWords;
        }
    }

    // If the result is empty (zero amount), return 'صفر فلس'
    if (result === '') {
        result = 'صفر فلس';
    }

    return 'فقط ' + result + ' لا غير';
}

// Format date
function formatDate(dateString) {
    if (!dateString || dateString === '-') return '-';

    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

// Check if date is overdue
function isOverdue(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return date < today;
}

// Get payment method text
function getPaymentMethodText(method) {
    if (!method) return '-';

    switch (method) {
        case 'cash':
            return 'نقداً';
        case 'bank_transfer':
            return 'تحويل بنكي';
        case 'check':
            return 'شيك';
        case 'other':
            return 'أخرى';
        default:
            return method;
    }
}
