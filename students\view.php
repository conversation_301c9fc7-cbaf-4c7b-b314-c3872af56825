<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get student ID from URL
$id = $_GET['id'] ?? 0;

// Check if student exists
$stmt = $pdo->prepare('SELECT * FROM students WHERE id = ?');
$stmt->execute([$id]);
$student = $stmt->fetch();

if (!$student) {
    // Set error message
    $_SESSION['flash_message'] = 'الطالب غير موجود';
    $_SESSION['flash_type'] = 'danger';
    
    // Redirect to student list
    header('Location: list.php');
    exit;
}

// Get student debt
$stmt = $pdo->prepare('SELECT 
    COALESCE(SUM(CASE WHEN type = "purchase" THEN amount ELSE 0 END), 0) as total_purchases, 
    COALESCE(SUM(CASE WHEN type = "payment" THEN amount ELSE 0 END), 0) as total_payments,
    COALESCE(SUM(CASE WHEN type = "purchase" THEN amount ELSE 0 END), 0) - 
    COALESCE(SUM(CASE WHEN type = "payment" THEN amount ELSE 0 END), 0) as debt
    FROM transactions
    WHERE student_id = ?');
$stmt->execute([$id]);
$debt_info = $stmt->fetch();

// Get transaction history
$stmt = $pdo->prepare('SELECT t.*, u.username as created_by_name
    FROM transactions t
    LEFT JOIN users u ON t.created_by = u.id
    WHERE t.student_id = ?
    ORDER BY t.date DESC, t.id DESC');
$stmt->execute([$id]);
$transactions = $stmt->fetchAll();

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>بيانات الطالب</h2>
            <div>
                <a href="../transactions/new.php?student_id=<?php echo $student['id']; ?>" class="btn btn-danger me-2">
                    <i class="fas fa-shopping-cart me-1"></i> تسجيل مشتريات
                </a>
                <a href="../transactions/payment.php?student_id=<?php echo $student['id']; ?>" class="btn btn-success me-2">
                    <i class="fas fa-money-bill-wave me-1"></i> تسجيل دفعة
                </a>
                <a href="edit.php?id=<?php echo $student['id']; ?>" class="btn btn-primary me-2">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>
                <a href="list.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
        </div>
        
        <!-- Student info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-user-graduate me-2"></i> معلومات الطالب
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 40%;">الاسم:</th>
                                <td><?php echo htmlspecialchars($student['name']); ?></td>
                            </tr>
                            <tr>
                                <th>الصف:</th>
                                <td><?php echo htmlspecialchars($student['class']); ?></td>
                            </tr>
                            <tr>
                                <th>رقم الهوية:</th>
                                <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                            </tr>
                            <tr>
                                <th>ولي الأمر:</th>
                                <td><?php echo htmlspecialchars($student['parent_name']); ?></td>
                            </tr>
                            <tr>
                                <th>رقم الجوال:</th>
                                <td><?php echo htmlspecialchars($student['phone_number']); ?></td>
                            </tr>
                            <tr>
                                <th>تاريخ التسجيل:</th>
                                <td><?php echo date('d/m/Y', strtotime($student['created_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-money-bill-wave me-2"></i> ملخص الحساب
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <h6 class="text-muted">إجمالي المشتريات</h6>
                                <h4 class="text-danger"><?php echo number_format($debt_info['total_purchases'], 2); ?> ريال</h4>
                            </div>
                            <div class="col-md-4 mb-3">
                                <h6 class="text-muted">إجمالي المدفوعات</h6>
                                <h4 class="text-success"><?php echo number_format($debt_info['total_payments'], 2); ?> ريال</h4>
                            </div>
                            <div class="col-md-4 mb-3">
                                <h6 class="text-muted">الرصيد الحالي</h6>
                                <?php if ($debt_info['debt'] > 0): ?>
                                    <h4 class="text-danger"><?php echo number_format($debt_info['debt'], 2); ?> ريال</h4>
                                    <small class="text-danger">مدين</small>
                                <?php elseif ($debt_info['debt'] < 0): ?>
                                    <h4 class="text-success"><?php echo number_format(abs($debt_info['debt']), 2); ?> ريال</h4>
                                    <small class="text-success">دائن (رصيد متبقي)</small>
                                <?php else: ?>
                                    <h4>0.00 ريال</h4>
                                    <small class="text-muted">متعادل</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="../reports/generate.php?student_id=<?php echo $student['id']; ?>" class="btn btn-outline-primary">
                                <i class="fas fa-file-pdf me-1"></i> إنشاء تقرير
                            </a>
                            <a href="../reports/notifications.php?student_id=<?php echo $student['id']; ?>" class="btn btn-outline-info">
                                <i class="fas fa-envelope me-1"></i> إرسال إشعار
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Transaction history -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-history me-2"></i> سجل المعاملات
            </div>
            <div class="card-body">
                <?php if (count($transactions) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الوصف</th>
                                    <th>بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($transaction['date'])); ?></td>
                                        <td>
                                            <?php if ($transaction['type'] === 'purchase'): ?>
                                                <span class="badge bg-danger">مشتريات</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">دفعة</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['type'] === 'purchase'): ?>
                                                <span class="text-danger"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                            <?php else: ?>
                                                <span class="text-success"><?php echo number_format($transaction['amount'], 2); ?> ريال</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير معروف'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        لا توجد معاملات مسجلة لهذا الطالب.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
