<?php
// Database configuration - Using SQLite for quick setup
$db_file = __DIR__ . '/../database/canteen_management.db';

// Create database directory if it doesn't exist
$db_dir = dirname($db_file);
if (!is_dir($db_dir)) {
    mkdir($db_dir, 0755, true);
}

try {
    // Create PDO instance with SQLite
    $pdo = new PDO("sqlite:$db_file");
    // Set PDO to throw exceptions on error
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Set default fetch mode to associative array
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Create tables if they don't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHA<PERSON>(50) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'cashier',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            class VARCHAR(50) NOT NULL,
            student_id VARCHAR(50) NOT NULL UNIQUE,
            parent_name VARCHAR(100) NOT NULL,
            phone_number VARCHAR(20) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            type VARCHAR(20) NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            description TEXT,
            date DATE NOT NULL,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
        );
    ");

    // Check if admin user exists, if not create it
    $stmt = $pdo->prepare('SELECT COUNT(*) FROM users WHERE email = ?');
    $stmt->execute(['<EMAIL>']);
    if ($stmt->fetchColumn() == 0) {
        // Insert default admin user (password: admin123)
        $stmt = $pdo->prepare('INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)');
        $stmt->execute(['admin', '<EMAIL>', '$2y$10$8tGmGEOg1LLgSgJwQYXlleWVjxjSUvHfJN1zIlHEYvGhsxJQZgX7.', 'admin']);

        // Insert sample students
        $students = [
            ['أحمد محمد', 'الصف الثالث', '1001', 'محمد أحمد', '0501234567'],
            ['سارة خالد', 'الصف الثاني', '1002', 'خالد سعيد', '0509876543'],
            ['عمر علي', 'الصف الأول', '1003', 'علي عمر', '0507654321'],
            ['فاطمة أحمد', 'الصف الثالث', '1004', 'أحمد محمود', '0501122334'],
            ['محمد سعيد', 'الصف الثاني', '1005', 'سعيد محمد', '0505544332']
        ];

        $stmt = $pdo->prepare('INSERT INTO students (name, class, student_id, parent_name, phone_number) VALUES (?, ?, ?, ?, ?)');
        foreach ($students as $student) {
            $stmt->execute($student);
        }
    }

} catch(PDOException $e) {
    // Display error message if connection fails
    die("Connection failed: " . $e->getMessage());
}
?>
