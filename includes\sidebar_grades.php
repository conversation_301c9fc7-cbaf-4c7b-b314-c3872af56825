<div class="col-md-3">
    <div class="card">
        <div class="card-header bg-primary text-white">
            القائمة الرئيسية
        </div>
        <div class="list-group list-group-flush">
            <a href="/dashboard.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
            </a>
            <a href="/students/list.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/students/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
            </a>
            <a href="/subjects/list.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/subjects/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-book me-2"></i> إدارة المواد
            </a>
            <a href="/assessment_types/list.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/assessment_types/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-tasks me-2"></i> أنواع التقييم
            </a>
            <a href="/grades/add.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/grades/add.php' ? 'active' : ''; ?>">
                <i class="fas fa-plus-circle me-2"></i> تسجيل درجات
            </a>
            <a href="/grades/list.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/grades/list.php' ? 'active' : ''; ?>">
                <i class="fas fa-list-alt me-2"></i> عرض الدرجات
            </a>
            <a href="/reports/generate.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/reports/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar me-2"></i> التقارير
            </a>
            <a href="/reports/notifications.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/reports/notifications.php' ? 'active' : ''; ?>">
                <i class="fas fa-bell me-2"></i> الإشعارات
            </a>
            <?php if (isAdmin()): ?>
            <a href="/users/list.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/users/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-users-cog me-2"></i> إدارة المستخدمين
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>
