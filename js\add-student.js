// Add student functionality

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }
    
    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });
    
    // Handle form submission
    document.getElementById('add-student-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const name = document.getElementById('name').value;
        const studentClass = document.getElementById('class').value;
        const studentId = document.getElementById('student_id').value;
        const parentName = document.getElementById('parent_name').value;
        const phoneNumber = document.getElementById('phone_number').value;
        
        // Validate form data
        const errors = [];
        
        if (!name) {
            errors.push('اسم الطالب مطلوب');
        }
        
        if (!studentClass) {
            errors.push('الصف مطلوب');
        }
        
        if (!studentId) {
            errors.push('رقم هوية الطالب مطلوب');
        } else {
            // Check if student_id already exists
            const students = JSON.parse(localStorage.getItem('students')) || [];
            if (students.some(student => student.student_id === studentId)) {
                errors.push('رقم هوية الطالب مستخدم بالفعل');
            }
        }
        
        if (!parentName) {
            errors.push('اسم ولي الأمر مطلوب');
        }
        
        if (!phoneNumber) {
            errors.push('رقم الجوال مطلوب');
        }
        
        // Display errors if any
        const errorAlert = document.getElementById('error-alert');
        const errorList = document.getElementById('error-list');
        
        if (errors.length > 0) {
            errorList.innerHTML = '';
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
            errorAlert.classList.remove('d-none');
            return;
        }
        
        // Hide error alert if no errors
        errorAlert.classList.add('d-none');
        
        // Get existing students from localStorage
        const students = JSON.parse(localStorage.getItem('students')) || [];
        
        // Generate new student ID
        const newId = students.length > 0 ? Math.max(...students.map(student => student.id)) + 1 : 1;
        
        // Create new student object
        const newStudent = {
            id: newId,
            name: name,
            class: studentClass,
            student_id: studentId,
            parent_name: parentName,
            phone_number: phoneNumber,
            created_at: new Date().toISOString()
        };
        
        // Add new student to array
        students.push(newStudent);
        
        // Save updated students array to localStorage
        localStorage.setItem('students', JSON.stringify(students));
        
        // Show success message and redirect
        alert('تم إضافة الطالب بنجاح');
        window.location.href = 'students.html';
    });
});
