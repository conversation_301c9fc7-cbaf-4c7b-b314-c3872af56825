// Invoices and Receipts functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

// Initialize documents in localStorage if not exists
if (!localStorage.getItem('documents')) {
    localStorage.setItem('documents', JSON.stringify([]));
}

// Direct event handlers for document actions

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set default date to today
    document.getElementById('document-date').valueAsDate = new Date();

    // Load students for dropdowns
    loadStudentsForDropdowns();

    // Load invoices and receipts
    loadInvoices();
    loadReceipts();

    // Handle document type change
    document.getElementById('document-type').addEventListener('change', function() {
        const paymentMethodRow = document.getElementById('payment-method-row');
        if (this.value === 'receipt') {
            paymentMethodRow.style.display = 'flex';
        } else {
            paymentMethodRow.style.display = 'none';
        }
    });

    // Handle create document form submission
    document.getElementById('create-document-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createDocument();
    });

    // Handle invoice filter
    document.getElementById('apply-invoice-filter').addEventListener('click', function() {
        loadInvoices();
    });

    // Handle receipt filter
    document.getElementById('apply-receipt-filter').addEventListener('click', function() {
        loadReceipts();
    });

    // Handle reset invoice filter
    document.getElementById('reset-invoice-filter').addEventListener('click', function() {
        document.getElementById('invoice-student-filter').value = '';
        document.getElementById('invoice-date-from').value = '';
        document.getElementById('invoice-date-to').value = '';
        loadInvoices();
    });

    // Handle reset receipt filter
    document.getElementById('reset-receipt-filter').addEventListener('click', function() {
        document.getElementById('receipt-student-filter').value = '';
        document.getElementById('receipt-date-from').value = '';
        document.getElementById('receipt-date-to').value = '';
        loadReceipts();
    });

    // Handle print document button
    document.getElementById('print-document-btn').addEventListener('click', function() {
        window.print();
    });

    // Handle reset documents data button
    document.getElementById('reset-documents-data').addEventListener('click', function() {
        if (confirm('هل أنت متأكد من إعادة تهيئة بيانات الفواتير والسندات؟ سيتم حذف جميع البيانات الحالية وإعادة تهيئتها بالبيانات الافتراضية.')) {
            // Clear documents data
            localStorage.removeItem('documents');

            // Reload the page to reinitialize sample data
            window.location.reload();
        }
    });
});

// Load students for dropdowns
function loadStudentsForDropdowns() {
    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Sort students by name
    students.sort((a, b) => a.name.localeCompare(b.name));

    // Populate document student dropdown
    const documentStudentSelect = document.getElementById('document-student');
    documentStudentSelect.innerHTML = '<option value="">اختر الطالب...</option>';

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        documentStudentSelect.appendChild(option);
    });

    // Populate invoice student filter dropdown
    const invoiceStudentFilter = document.getElementById('invoice-student-filter');
    invoiceStudentFilter.innerHTML = '<option value="">جميع الطلاب</option>';

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        invoiceStudentFilter.appendChild(option);
    });

    // Populate receipt student filter dropdown
    const receiptStudentFilter = document.getElementById('receipt-student-filter');
    receiptStudentFilter.innerHTML = '<option value="">جميع الطلاب</option>';

    students.forEach(student => {
        const option = document.createElement('option');
        option.value = student.id;
        option.textContent = `${student.name} - ${student.class}`;
        receiptStudentFilter.appendChild(option);
    });
}

// Load invoices
function loadInvoices() {
    const documents = JSON.parse(localStorage.getItem('documents')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Get filter values
    const studentId = document.getElementById('invoice-student-filter').value;
    const dateFrom = document.getElementById('invoice-date-from').value;
    const dateTo = document.getElementById('invoice-date-to').value;

    // Filter invoices
    let invoices = documents.filter(doc => doc.type === 'invoice');

    if (studentId) {
        invoices = invoices.filter(invoice => invoice.student_id == studentId);
    }

    if (dateFrom) {
        invoices = invoices.filter(invoice => invoice.date >= dateFrom);
    }

    if (dateTo) {
        invoices = invoices.filter(invoice => invoice.date <= dateTo);
    }

    // Sort invoices by date (newest first)
    invoices.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Display invoices
    const tableBody = document.getElementById('invoices-table-body');
    tableBody.innerHTML = '';

    if (invoices.length > 0) {
        invoices.forEach(invoice => {
            const student = students.find(s => s.id == invoice.student_id) || { name: 'غير معروف', class: '-' };

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${invoice.document_number}</td>
                <td>${formatDate(invoice.date)}</td>
                <td>${student.name} - ${student.class}</td>
                <td>${formatKuwaitiCurrency(invoice.amount)}</td>
                <td><span class="badge bg-success">مدفوعة</span></td>
                <td>
                    <button type="button" onclick="viewDocument(${invoice.id})" class="btn btn-sm btn-info" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" onclick="printDocument(${invoice.id})" class="btn btn-sm btn-primary" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد فواتير مطابقة لمعايير البحث</td></tr>';
    }
}

// Load receipts
function loadReceipts() {
    const documents = JSON.parse(localStorage.getItem('documents')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];

    // Get filter values
    const studentId = document.getElementById('receipt-student-filter').value;
    const dateFrom = document.getElementById('receipt-date-from').value;
    const dateTo = document.getElementById('receipt-date-to').value;

    // Filter receipts
    let receipts = documents.filter(doc => doc.type === 'receipt');

    if (studentId) {
        receipts = receipts.filter(receipt => receipt.student_id == studentId);
    }

    if (dateFrom) {
        receipts = receipts.filter(receipt => receipt.date >= dateFrom);
    }

    if (dateTo) {
        receipts = receipts.filter(receipt => receipt.date <= dateTo);
    }

    // Sort receipts by date (newest first)
    receipts.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Display receipts
    const tableBody = document.getElementById('receipts-table-body');
    tableBody.innerHTML = '';

    if (receipts.length > 0) {
        receipts.forEach(receipt => {
            const student = students.find(s => s.id == receipt.student_id) || { name: 'غير معروف', class: '-' };

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${receipt.document_number}</td>
                <td>${formatDate(receipt.date)}</td>
                <td>${student.name} - ${student.class}</td>
                <td>${formatKuwaitiCurrency(receipt.amount)}</td>
                <td>${getPaymentMethodText(receipt.payment_method)}</td>
                <td>
                    <button type="button" onclick="viewDocument(${receipt.id})" class="btn btn-sm btn-info" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" onclick="printDocument(${receipt.id})" class="btn btn-sm btn-primary" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    } else {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد سندات قبض مطابقة لمعايير البحث</td></tr>';
    }
}

// Create document
function createDocument() {
    const documentType = document.getElementById('document-type').value;
    const studentId = document.getElementById('document-student').value;
    const date = document.getElementById('document-date').value;
    const amountInFils = parseFloat(document.getElementById('document-amount').value);
    const amount = amountInFils / 1000; // تحويل من فلس إلى دينار
    const description = document.getElementById('document-description').value;

    // Validate form
    if (!documentType || !studentId || !date || isNaN(amount) || amount <= 0) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Get payment method for receipts
    let paymentMethod = null;
    let referenceNumber = null;

    if (documentType === 'receipt') {
        paymentMethod = document.getElementById('payment-method').value;
        referenceNumber = document.getElementById('reference-number').value;
    }

    // Get existing documents
    const documents = JSON.parse(localStorage.getItem('documents')) || [];

    // Generate document number
    const documentPrefix = documentType === 'invoice' ? 'INV-' : 'REC-';
    const documentNumber = `${documentPrefix}${new Date().getFullYear()}-${(documents.length + 1).toString().padStart(4, '0')}`;

    // Generate document ID
    const documentId = documents.length > 0 ? Math.max(...documents.map(doc => doc.id)) + 1 : 1;

    // Create document object
    const newDocument = {
        id: documentId,
        type: documentType,
        document_number: documentNumber,
        student_id: parseInt(studentId),
        date: date,
        amount: amount,
        description: description,
        created_by: JSON.parse(sessionStorage.getItem('currentUser')).id,
        created_at: new Date().toISOString()
    };

    // Add payment method for receipts
    if (documentType === 'receipt') {
        newDocument.payment_method = paymentMethod;
        newDocument.reference_number = referenceNumber;
    }

    // Add document to array
    documents.push(newDocument);

    // Save documents to localStorage
    localStorage.setItem('documents', JSON.stringify(documents));

    // If it's a receipt, also create a payment transaction
    if (documentType === 'receipt') {
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

        // Generate transaction ID
        const transactionId = transactions.length > 0 ? Math.max(...transactions.map(t => t.id)) + 1 : 1;

        // Create transaction object
        const newTransaction = {
            id: transactionId,
            student_id: parseInt(studentId),
            type: 'payment',
            amount: amount,
            description: `سند قبض رقم ${documentNumber}${description ? ' - ' + description : ''}`,
            date: date,
            created_by: JSON.parse(sessionStorage.getItem('currentUser')).id,
            created_at: new Date().toISOString()
        };

        // Add transaction to array
        transactions.push(newTransaction);

        // Save transactions to localStorage
        localStorage.setItem('transactions', JSON.stringify(transactions));
    }

    // Show document preview
    viewDocument(documentId);

    // Reset form
    document.getElementById('create-document-form').reset();
    document.getElementById('document-date').valueAsDate = new Date();
    document.getElementById('payment-method-row').style.display = 'none';

    // Reload lists
    loadInvoices();
    loadReceipts();
}

// View document - defined as a global function
window.viewDocument = function(documentId) {
    const documents = JSON.parse(localStorage.getItem('documents')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];

    // Find document
    const document = documents.find(doc => doc.id == documentId);

    if (!document) {
        alert('المستند غير موجود');
        return;
    }

    // Find student
    const student = students.find(s => s.id == document.student_id) || { name: 'غير معروف', class: '-', parent_name: '-', phone_number: '-' };

    // Find user
    const user = users.find(u => u.id == document.created_by) || { username: 'غير معروف' };

    // Create document preview
    const previewContent = document.getElementById('document-preview-content');

    if (document.type === 'invoice') {
        // Invoice preview
        previewContent.innerHTML = `
            <div class="invoice-preview">
                <div class="text-center mb-4">
                    <h3>فاتورة مشتريات</h3>
                    <p class="mb-0">رقم الفاتورة: ${document.document_number}</p>
                    <p>التاريخ: ${formatDate(document.date)}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>بيانات الطالب</h5>
                        <p class="mb-1"><strong>الاسم:</strong> ${student.name}</p>
                        <p class="mb-1"><strong>الصف:</strong> ${student.class}</p>
                        <p class="mb-1"><strong>ولي الأمر:</strong> ${student.parent_name}</p>
                        <p class="mb-1"><strong>رقم الجوال:</strong> ${student.phone_number}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h5>بيانات المدرسة</h5>
                        <p class="mb-1">مدرسة النموذجية</p>
                        <p class="mb-1">العنوان: الكويت</p>
                        <p class="mb-1">الهاتف: 123456789</p>
                        <p class="mb-1">البريد الإلكتروني: <EMAIL></p>
                    </div>
                </div>

                <div class="table-responsive mb-4">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>البيان</th>
                                <th class="text-end">المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>${document.description || 'مشتريات من المقصف المدرسي'}</td>
                                <td class="text-end">${formatKuwaitiCurrency(document.amount)}</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>الإجمالي</th>
                                <th class="text-end">${formatKuwaitiCurrency(document.amount)}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <p><strong>ملاحظات:</strong></p>
                        <p>تم تسجيل هذه الفاتورة في نظام إدارة ديون المقصف المدرسي.</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <p><strong>أنشئت بواسطة:</strong> ${user.username}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${new Date(document.created_at).toLocaleString('ar-KW')}</p>
                    </div>
                </div>
            </div>
        `;
    } else {
        // Receipt preview
        previewContent.innerHTML = `
            <div class="receipt-preview">
                <div class="text-center mb-4">
                    <h3>سند قبض</h3>
                    <p class="mb-0">رقم السند: ${document.document_number}</p>
                    <p>التاريخ: ${formatDate(document.date)}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>بيانات الطالب</h5>
                        <p class="mb-1"><strong>الاسم:</strong> ${student.name}</p>
                        <p class="mb-1"><strong>الصف:</strong> ${student.class}</p>
                        <p class="mb-1"><strong>ولي الأمر:</strong> ${student.parent_name}</p>
                        <p class="mb-1"><strong>رقم الجوال:</strong> ${student.phone_number}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h5>بيانات المدرسة</h5>
                        <p class="mb-1">مدرسة النموذجية</p>
                        <p class="mb-1">العنوان: الكويت</p>
                        <p class="mb-1">الهاتف: 123456789</p>
                        <p class="mb-1">البريد الإلكتروني: <EMAIL></p>
                    </div>
                </div>

                <div class="alert alert-success mb-4">
                    <p class="mb-0 text-center fs-5">استلمنا من ولي أمر الطالب ${student.name} مبلغ وقدره ${formatKuwaitiCurrency(document.amount)}</p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(document.payment_method)}</p>
                        ${document.reference_number ? `<p><strong>رقم المرجع:</strong> ${document.reference_number}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <p><strong>البيان:</strong> ${document.description || 'سداد ديون المقصف المدرسي'}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="border-top pt-2 mt-5 text-center">
                            <p>توقيع المستلم</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border-top pt-2 mt-5 text-center">
                            <p>ختم المدرسة</p>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-8">
                        <p><strong>ملاحظات:</strong></p>
                        <p>تم تسجيل هذا السند في نظام إدارة ديون المقصف المدرسي.</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <p><strong>أنشئ بواسطة:</strong> ${user.username}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${new Date(document.created_at).toLocaleString('ar-KW')}</p>
                    </div>
                </div>
            </div>
        `;
    }

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('documentPreviewModal'));
    modal.show();
}

// Print document - defined as a global function
window.printDocument = function(documentId) {
    // View document first
    viewDocument(documentId);

    // Then trigger print
    setTimeout(() => {
        document.getElementById('print-document-btn').click();
    }, 500);
}

// Get payment method text
function getPaymentMethodText(method) {
    switch (method) {
        case 'cash':
            return 'نقداً';
        case 'bank_transfer':
            return 'تحويل بنكي';
        case 'check':
            return 'شيك';
        case 'other':
            return 'أخرى';
        default:
            return method;
    }
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}
