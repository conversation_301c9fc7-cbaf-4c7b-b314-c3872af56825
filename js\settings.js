// Settings functionality

// Global variables
let currentUser;
let settings = {};
let defaultSettings = {
    general: {
        schoolName: 'مدرسة نموذجية',
        schoolLogo: 'img/default-logo.png',
        schoolAddress: 'الكويت',
        schoolPhone: '+96512345678',
        schoolEmail: '<EMAIL>',
        academicYear: '2023-2024'
    },
    currency: {
        name: 'دينار كويتي',
        code: 'KWD',
        symbol: 'د.ك',
        decimalPlaces: 3,
        decimalSeparator: '.',
        thousandsSeparator: ',',
        position: 'after',
        smallCurrencyName: 'فلس',
        smallCurrencyRatio: 1000,
        showSmallCurrency: true
    },
    notifications: {
        enabled: true,
        smsEnabled: false,
        whatsappEnabled: true,
        defaultCountryCode: '+965',
        autoDebtNotifications: true,
        debtNotificationThreshold: 5.000,
        autoInstallmentNotifications: true,
        installmentNotificationDays: 3,
        debtNotificationTemplate: 'عزيزي ولي أمر الطالب {student_name}، نود إعلامكم بأن هناك مبلغ {debt_amount} مستحق عليكم لصالح المقصف المدرسي. نرجو سداد المبلغ في أقرب وقت ممكن. مع الشكر.',
        installmentNotificationTemplate: 'عزيزي ولي أمر الطالب {student_name}، نود تذكيركم بموعد استحقاق القسط رقم {installment_number} بمبلغ {installment_amount} بتاريخ {due_date}. مع الشكر.',
        paymentNotificationTemplate: 'عزيزي ولي أمر الطالب {student_name}، نود إعلامكم بأنه تم استلام دفعة بمبلغ {payment_amount} بتاريخ {payment_date}. الرصيد المتبقي: {remaining_balance}. مع الشكر.'
    },
    system: {
        dataRetentionPeriod: 36,
        enableAutoBackup: true,
        backupFrequency: 'weekly',
        backupLocation: './backups'
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    } else {
        // Redirect to login if not logged in
        window.location.href = 'index.html';
        return;
    }
    
    // Check if user has admin role
    if (currentUser.role !== 'admin') {
        alert('ليس لديك صلاحية الوصول إلى هذه الصفحة');
        window.location.href = 'dashboard.html';
        return;
    }
    
    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });
    
    // Load settings
    loadSettings();
    
    // Setup event listeners
    setupEventListeners();
});

// Load settings from localStorage
function loadSettings() {
    // Get settings from localStorage or use defaults
    const storedSettings = localStorage.getItem('settings');
    
    if (storedSettings) {
        settings = JSON.parse(storedSettings);
    } else {
        settings = { ...defaultSettings };
        localStorage.setItem('settings', JSON.stringify(settings));
    }
    
    // Populate form fields
    populateGeneralSettings();
    populateCurrencySettings();
    populateNotificationsSettings();
    populateSystemSettings();
}

// Populate general settings form
function populateGeneralSettings() {
    const general = settings.general;
    
    document.getElementById('school-name').value = general.schoolName;
    // Logo preview will be handled separately
    document.getElementById('logo-preview').src = general.schoolLogo;
    document.getElementById('school-address').value = general.schoolAddress;
    document.getElementById('school-phone').value = general.schoolPhone;
    document.getElementById('school-email').value = general.schoolEmail;
    document.getElementById('academic-year').value = general.academicYear;
    
    // Handle logo file input change
    document.getElementById('school-logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('logo-preview').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
}

// Populate currency settings form
function populateCurrencySettings() {
    const currency = settings.currency;
    
    document.getElementById('currency-name').value = currency.name;
    document.getElementById('currency-code').value = currency.code;
    document.getElementById('currency-symbol').value = currency.symbol;
    document.getElementById('decimal-places').value = currency.decimalPlaces;
    document.getElementById('decimal-separator').value = currency.decimalSeparator;
    document.getElementById('thousands-separator').value = currency.thousandsSeparator;
    document.getElementById('currency-position').value = currency.position;
    document.getElementById('small-currency-name').value = currency.smallCurrencyName;
    document.getElementById('small-currency-ratio').value = currency.smallCurrencyRatio;
    document.getElementById('show-small-currency').checked = currency.showSmallCurrency;
}

// Populate notifications settings form
function populateNotificationsSettings() {
    const notifications = settings.notifications;
    
    document.getElementById('enable-notifications').checked = notifications.enabled;
    document.getElementById('enable-sms-notifications').checked = notifications.smsEnabled;
    document.getElementById('enable-whatsapp-notifications').checked = notifications.whatsappEnabled;
    document.getElementById('default-country-code').value = notifications.defaultCountryCode;
    document.getElementById('auto-debt-notifications').checked = notifications.autoDebtNotifications;
    document.getElementById('debt-notification-threshold').value = notifications.debtNotificationThreshold;
    document.getElementById('auto-installment-notifications').checked = notifications.autoInstallmentNotifications;
    document.getElementById('installment-notification-days').value = notifications.installmentNotificationDays;
    document.getElementById('debt-notification-template').value = notifications.debtNotificationTemplate;
    document.getElementById('installment-notification-template').value = notifications.installmentNotificationTemplate;
    document.getElementById('payment-notification-template').value = notifications.paymentNotificationTemplate;
}

// Populate system settings form
function populateSystemSettings() {
    const system = settings.system;
    
    document.getElementById('data-retention-period').value = system.dataRetentionPeriod;
    document.getElementById('enable-auto-backup').checked = system.enableAutoBackup;
    document.getElementById('backup-frequency').value = system.backupFrequency;
    document.getElementById('backup-location').value = system.backupLocation;
}

// Setup event listeners
function setupEventListeners() {
    // Save settings button
    document.getElementById('save-settings-btn').addEventListener('click', saveSettings);
    
    // Backup now button
    document.getElementById('backup-now-btn').addEventListener('click', createBackup);
    
    // Restore backup button
    document.getElementById('restore-backup-btn').addEventListener('click', function() {
        document.getElementById('backup-file').click();
    });
    
    // Backup file input change
    document.getElementById('backup-file').addEventListener('change', restoreBackup);
    
    // Clear all data button
    document.getElementById('clear-all-data-btn').addEventListener('click', function() {
        showConfirmationModal('هل أنت متأكد من أنك تريد حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.', clearAllData);
    });
    
    // Reset settings button
    document.getElementById('reset-settings-btn').addEventListener('click', function() {
        showConfirmationModal('هل أنت متأكد من أنك تريد إعادة تعيين الإعدادات الافتراضية؟', resetSettings);
    });
}

// Save settings
function saveSettings() {
    // Get values from general settings form
    settings.general = {
        schoolName: document.getElementById('school-name').value,
        schoolLogo: document.getElementById('logo-preview').src,
        schoolAddress: document.getElementById('school-address').value,
        schoolPhone: document.getElementById('school-phone').value,
        schoolEmail: document.getElementById('school-email').value,
        academicYear: document.getElementById('academic-year').value
    };
    
    // Get values from currency settings form
    settings.currency = {
        name: document.getElementById('currency-name').value,
        code: document.getElementById('currency-code').value,
        symbol: document.getElementById('currency-symbol').value,
        decimalPlaces: parseInt(document.getElementById('decimal-places').value),
        decimalSeparator: document.getElementById('decimal-separator').value,
        thousandsSeparator: document.getElementById('thousands-separator').value,
        position: document.getElementById('currency-position').value,
        smallCurrencyName: document.getElementById('small-currency-name').value,
        smallCurrencyRatio: parseInt(document.getElementById('small-currency-ratio').value),
        showSmallCurrency: document.getElementById('show-small-currency').checked
    };
    
    // Get values from notifications settings form
    settings.notifications = {
        enabled: document.getElementById('enable-notifications').checked,
        smsEnabled: document.getElementById('enable-sms-notifications').checked,
        whatsappEnabled: document.getElementById('enable-whatsapp-notifications').checked,
        defaultCountryCode: document.getElementById('default-country-code').value,
        autoDebtNotifications: document.getElementById('auto-debt-notifications').checked,
        debtNotificationThreshold: parseFloat(document.getElementById('debt-notification-threshold').value),
        autoInstallmentNotifications: document.getElementById('auto-installment-notifications').checked,
        installmentNotificationDays: parseInt(document.getElementById('installment-notification-days').value),
        debtNotificationTemplate: document.getElementById('debt-notification-template').value,
        installmentNotificationTemplate: document.getElementById('installment-notification-template').value,
        paymentNotificationTemplate: document.getElementById('payment-notification-template').value
    };
    
    // Get values from system settings form
    settings.system = {
        dataRetentionPeriod: parseInt(document.getElementById('data-retention-period').value),
        enableAutoBackup: document.getElementById('enable-auto-backup').checked,
        backupFrequency: document.getElementById('backup-frequency').value,
        backupLocation: document.getElementById('backup-location').value
    };
    
    // Save settings to localStorage
    localStorage.setItem('settings', JSON.stringify(settings));
    
    // Show success message
    alert('تم حفظ الإعدادات بنجاح');
    
    // Log activity
    logActivity('update', 'settings', 'تم تحديث إعدادات النظام');
}

// Create backup
function createBackup() {
    // Get all data from localStorage
    const backup = {
        settings: JSON.parse(localStorage.getItem('settings')) || defaultSettings,
        students: JSON.parse(localStorage.getItem('students')) || [],
        transactions: JSON.parse(localStorage.getItem('transactions')) || [],
        installmentPlans: JSON.parse(localStorage.getItem('installmentPlans')) || [],
        users: JSON.parse(localStorage.getItem('users')) || [],
        roles: JSON.parse(localStorage.getItem('roles')) || [],
        activityLog: JSON.parse(localStorage.getItem('activityLog')) || [],
        timestamp: new Date().toISOString(),
        version: '1.0'
    };
    
    // Convert to JSON string
    const backupJson = JSON.stringify(backup);
    
    // Create a blob and download link
    const blob = new Blob([backupJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup_${formatDateForFilename(new Date())}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // Show success message
    alert('تم إنشاء نسخة احتياطية بنجاح');
    
    // Log activity
    logActivity('create', 'backup', 'تم إنشاء نسخة احتياطية');
}

// Restore backup
function restoreBackup(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backup = JSON.parse(e.target.result);
            
            // Validate backup format
            if (!backup.version || !backup.timestamp) {
                throw new Error('تنسيق النسخة الاحتياطية غير صالح');
            }
            
            // Confirm restore
            showConfirmationModal(
                `هل أنت متأكد من أنك تريد استعادة النسخة الاحتياطية المؤرخة بتاريخ ${formatDate(backup.timestamp)}؟ سيتم استبدال جميع البيانات الحالية.`,
                function() {
                    // Restore data
                    if (backup.settings) localStorage.setItem('settings', JSON.stringify(backup.settings));
                    if (backup.students) localStorage.setItem('students', JSON.stringify(backup.students));
                    if (backup.transactions) localStorage.setItem('transactions', JSON.stringify(backup.transactions));
                    if (backup.installmentPlans) localStorage.setItem('installmentPlans', JSON.stringify(backup.installmentPlans));
                    if (backup.users) localStorage.setItem('users', JSON.stringify(backup.users));
                    if (backup.roles) localStorage.setItem('roles', JSON.stringify(backup.roles));
                    if (backup.activityLog) localStorage.setItem('activityLog', JSON.stringify(backup.activityLog));
                    
                    // Show success message
                    alert('تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل الصفحة.');
                    
                    // Log activity
                    logActivity('update', 'restore', 'تم استعادة نسخة احتياطية');
                    
                    // Reload page
                    window.location.reload();
                }
            );
        } catch (error) {
            alert('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// Clear all data
function clearAllData() {
    // Keep settings and users
    const settings = localStorage.getItem('settings');
    const users = localStorage.getItem('users');
    const roles = localStorage.getItem('roles');
    
    // Clear localStorage
    localStorage.clear();
    
    // Restore settings and users
    if (settings) localStorage.setItem('settings', settings);
    if (users) localStorage.setItem('users', users);
    if (roles) localStorage.setItem('roles', roles);
    
    // Show success message
    alert('تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
    
    // Log activity
    logActivity('delete', 'data', 'تم حذف جميع البيانات');
    
    // Reload page
    window.location.reload();
}

// Reset settings
function resetSettings() {
    // Reset settings to defaults
    settings = { ...defaultSettings };
    localStorage.setItem('settings', JSON.stringify(settings));
    
    // Show success message
    alert('تم إعادة تعيين الإعدادات الافتراضية بنجاح. سيتم إعادة تحميل الصفحة.');
    
    // Log activity
    logActivity('update', 'settings', 'تم إعادة تعيين الإعدادات الافتراضية');
    
    // Reload page
    window.location.reload();
}

// Show confirmation modal
function showConfirmationModal(message, confirmCallback) {
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    document.getElementById('confirmation-message').textContent = message;
    
    // Remove previous event listener
    const confirmBtn = document.getElementById('confirm-action-btn');
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // Add new event listener
    newConfirmBtn.addEventListener('click', function() {
        modal.hide();
        confirmCallback();
    });
    
    modal.show();
}

// Log activity
function logActivity(type, target, description) {
    // Get activity log from localStorage
    let activityLog = JSON.parse(localStorage.getItem('activityLog')) || [];
    
    // Add new activity
    activityLog.push({
        id: Date.now(),
        timestamp: new Date().toISOString(),
        userId: currentUser.id,
        username: currentUser.username,
        type: type,
        target: target,
        description: description,
        ip: '127.0.0.1' // In a real app, this would be the actual IP
    });
    
    // Save activity log to localStorage
    localStorage.setItem('activityLog', JSON.stringify(activityLog));
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
}

// Format date for filename
function formatDateForFilename(date) {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}_${date.getHours().toString().padStart(2, '0')}-${date.getMinutes().toString().padStart(2, '0')}`;
}
