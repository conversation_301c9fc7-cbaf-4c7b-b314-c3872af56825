// سكريبت لاستيراد بيانات الطلاب والديون

// بيانات الطلاب والديون
const studentsData = [
    { name: "عبد العزيز الخراز", debt: 1400000, paid: 0, remaining: 1400000 },
    { name: "عبدالله الانصارى", debt: 1050000, paid: 0, remaining: 1050000 },
    { name: "على سالم", debt: 1300000, paid: 0, remaining: 1300000 },
    { name: "جاسم القلاف", debt: 250000, paid: 0, remaining: 250000 },
    { name: "نايف الحجرف", debt: 400000, paid: 0, remaining: 400000 },
    { name: "عبدالله المنصورى", debt: 450000, paid: 0, remaining: 450000 },
    { name: "دعيج", debt: 700000, paid: 700000, remaining: 0 },
    { name: "فارس", debt: 150000, paid: 150000, remaining: 0 },
    { name: "على البصرى", debt: 750000, paid: 0, remaining: 750000 },
    { name: "عبد الله الرشيدي", debt: 1650000, paid: 0, remaining: 1650000 },
    { name: "سلطان المطيرى", debt: 1200000, paid: 0, remaining: 1200000 },
    { name: "صقر النجادة", debt: 600000, paid: 0, remaining: 600000 },
    { name: "مبارك مشارى", debt: 1600000, paid: 1600000, remaining: 0 },
    { name: "عبد العزيز يوسف", debt: 200000, paid: 200000, remaining: 0 },
    { name: "حسين الوزان", debt: 150000, paid: 150000, remaining: 0 },
    { name: "فهد الرغيب", debt: 1550000, paid: 0, remaining: 1550000 },
    { name: "محمد طلال", debt: 2500000, paid: 2500000, remaining: 0 },
    { name: "عيسى الراندى", debt: 750000, paid: 450000, remaining: 300000 },
    { name: "محمد العجمي", debt: 900000, paid: 0, remaining: 900000 },
    { name: "سلمان ملا", debt: 1800000, paid: 1250000, remaining: 550000 }
];

// وظيفة لاستيراد البيانات
function importData() {
    // حذف البيانات الموجودة سابقاً
    localStorage.removeItem('students');
    localStorage.removeItem('transactions');
    
    // إنشاء مصفوفة الطلاب
    const students = [];
    
    // إنشاء مصفوفة المعاملات
    const transactions = [];
    
    // إضافة المستخدم الحالي (إذا لم يكن موجوداً)
    let currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (!currentUser) {
        currentUser = {
            id: 1,
            username: 'admin',
            password: 'admin123',
            name: 'مدير النظام',
            role: 'admin'
        };
        sessionStorage.setItem('currentUser', JSON.stringify(currentUser));
    }
    
    // إضافة المستخدمين (إذا لم تكن موجودة)
    let users = JSON.parse(localStorage.getItem('users'));
    if (!users || users.length === 0) {
        users = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                name: 'مدير النظام',
                role: 'admin'
            }
        ];
        localStorage.setItem('users', JSON.stringify(users));
    }
    
    // إنشاء الطلاب والمعاملات
    studentsData.forEach((data, index) => {
        // إنشاء معرف فريد للطالب
        const studentId = index + 1;
        
        // إضافة الطالب
        students.push({
            id: studentId,
            name: data.name,
            class: 'الصف ' + (Math.floor(Math.random() * 12) + 1),
            student_id: 'S' + (10000 + studentId),
            parent_name: 'ولي أمر ' + data.name,
            phone_number: '965' + Math.floor(Math.random() * 10000000 + 50000000),
            created_at: new Date().toISOString()
        });
        
        // إضافة معاملة الشراء (الدين)
        if (data.debt > 0) {
            transactions.push({
                id: transactions.length + 1,
                student_id: studentId,
                type: 'purchase',
                amount: data.debt / 1000, // تحويل من فلس إلى دينار
                description: 'دين مستورد',
                date: new Date().toISOString().split('T')[0],
                created_by: currentUser.id,
                created_at: new Date().toISOString()
            });
        }
        
        // إضافة معاملة الدفع
        if (data.paid > 0) {
            transactions.push({
                id: transactions.length + 1,
                student_id: studentId,
                type: 'payment',
                amount: data.paid / 1000, // تحويل من فلس إلى دينار
                description: 'دفعة مستوردة',
                date: new Date().toISOString().split('T')[0],
                created_by: currentUser.id,
                created_at: new Date().toISOString()
            });
        }
    });
    
    // حفظ البيانات في localStorage
    localStorage.setItem('students', JSON.stringify(students));
    localStorage.setItem('transactions', JSON.stringify(transactions));
    
    // عرض رسالة نجاح
    alert('تم استيراد البيانات بنجاح!');
    
    // إعادة تحميل الصفحة
    window.location.reload();
}

// استدعاء وظيفة الاستيراد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة زر الاستيراد إلى الصفحة
    const importButton = document.createElement('button');
    importButton.className = 'btn btn-warning position-fixed bottom-0 end-0 m-3';
    importButton.innerHTML = '<i class="fas fa-file-import me-1"></i> استيراد البيانات';
    importButton.onclick = importData;
    document.body.appendChild(importButton);
});
