<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد وتصدير البيانات - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="invoices.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-invoice me-2"></i> الفواتير وسندات القبض
                        </a>
                        <a href="import-export.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-excel me-2"></i> استيراد/تصدير البيانات
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9">
                <h2 class="mb-4">استيراد وتصدير البيانات</h2>
                
                <!-- Export Data -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-file-export me-2"></i> تصدير البيانات
                    </div>
                    <div class="card-body">
                        <p class="mb-4">يمكنك تصدير البيانات بتنسيق Excel أو PDF للاستخدام في برامج أخرى أو للطباعة.</p>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-3x mb-3 text-primary"></i>
                                        <h5 class="card-title">بيانات الطلاب</h5>
                                        <p class="card-text">تصدير قائمة الطلاب مع معلوماتهم وأرصدتهم الحالية.</p>
                                        <div class="d-flex justify-content-center">
                                            <button class="btn btn-outline-success me-2" id="export-students-excel">
                                                <i class="fas fa-file-excel me-1"></i> Excel
                                            </button>
                                            <button class="btn btn-outline-danger" id="export-students-pdf">
                                                <i class="fas fa-file-pdf me-1"></i> PDF
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-exchange-alt fa-3x mb-3 text-primary"></i>
                                        <h5 class="card-title">سجل المعاملات</h5>
                                        <p class="card-text">تصدير سجل المعاملات (المشتريات والدفعات) لجميع الطلاب.</p>
                                        <div class="d-flex justify-content-center">
                                            <button class="btn btn-outline-success me-2" id="export-transactions-excel">
                                                <i class="fas fa-file-excel me-1"></i> Excel
                                            </button>
                                            <button class="btn btn-outline-danger" id="export-transactions-pdf">
                                                <i class="fas fa-file-pdf me-1"></i> PDF
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Import Data -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-file-import me-2"></i> استيراد البيانات
                    </div>
                    <div class="card-body">
                        <p class="mb-4">يمكنك استيراد بيانات الطلاب من ملف Excel. يرجى استخدام النموذج المقدم للتأكد من تنسيق البيانات بشكل صحيح.</p>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-graduate fa-3x mb-3 text-primary"></i>
                                        <h5 class="card-title">استيراد بيانات الطلاب</h5>
                                        <p class="card-text">استيراد قائمة الطلاب من ملف Excel.</p>
                                        <div class="mb-3">
                                            <a href="#" id="download-template" class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-download me-1"></i> تنزيل نموذج
                                            </a>
                                        </div>
                                        <div class="input-group">
                                            <input type="file" class="form-control" id="import-students-file" accept=".xlsx, .xls, .csv">
                                            <button class="btn btn-primary" type="button" id="import-students-btn">
                                                <i class="fas fa-upload me-1"></i> استيراد
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">تعليمات الاستيراد</h5>
                                        <ol class="mb-0">
                                            <li>قم بتنزيل نموذج ملف Excel.</li>
                                            <li>أدخل بيانات الطلاب في الأعمدة المناسبة.</li>
                                            <li>تأكد من عدم ترك أي خلايا فارغة في الحقول الإلزامية.</li>
                                            <li>احفظ الملف بتنسيق Excel (.xlsx أو .xls) أو CSV.</li>
                                            <li>اختر الملف واضغط على زر "استيراد".</li>
                                        </ol>
                                        <div class="alert alert-warning mt-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>تنبيه:</strong> سيتم تجاهل الطلاب الذين لديهم نفس رقم الهوية الموجود مسبقاً في النظام.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Backup & Restore -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-database me-2"></i> النسخ الاحتياطي واستعادة البيانات
                    </div>
                    <div class="card-body">
                        <p>يمكنك إنشاء نسخة احتياطية من جميع بيانات النظام واستعادتها لاحقاً.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-success w-100" id="backup-data-btn">
                                    <i class="fas fa-download me-1"></i> إنشاء نسخة احتياطية
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="file" class="form-control" id="restore-file" accept=".json">
                                    <button class="btn btn-warning" type="button" id="restore-data-btn">
                                        <i class="fas fa-upload me-1"></i> استعادة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SheetJS (xlsx) for Excel handling -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- jsPDF for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/import-export.js"></script>
</body>
</html>
