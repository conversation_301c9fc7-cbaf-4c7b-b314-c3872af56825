// Students management functionality

// Currency format - Kuwaiti Fils
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Load students data
    loadStudentsData();

    // Setup search functionality
    document.getElementById('studentSearch').addEventListener('keyup', function() {
        if (this.value.length >= 2 || this.value.length === 0) {
            filterStudents();
        }
    });

    // Setup filter buttons
    document.getElementById('applyFilters').addEventListener('click', filterStudents);
    document.getElementById('resetFilters').addEventListener('click', resetFilters);

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Load students data
function loadStudentsData() {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Calculate student debts
    const studentDebts = {};

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            debt: 0,
            total_purchases: 0,
            total_payments: 0
        };
    });

    // Calculate debts from transactions
    transactions.forEach(transaction => {
        const amount = parseFloat(transaction.amount);

        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].debt += amount;
            studentDebts[transaction.student_id].total_purchases += amount;
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].debt -= amount;
            studentDebts[transaction.student_id].total_payments += amount;
        }
    });

    // Store student debts in window object for filtering
    window.studentDebtsData = studentDebts;

    // Populate students table
    const tableBody = document.getElementById('students-table-body');
    tableBody.innerHTML = '';

    if (students.length > 0) {
        students.forEach(student => {
            const studentDebt = studentDebts[student.id] || { debt: 0 };
            const debt = studentDebt.debt;

            const row = document.createElement('tr');
            row.className = 'student-row';
            row.setAttribute('data-class', student.class);
            row.setAttribute('data-id', student.id);
            row.setAttribute('data-debt', debt);
            row.innerHTML = `
                <td class="student-name">${student.name}</td>
                <td>${student.class}</td>
                <td class="student-id">${student.student_id}</td>
                <td>${student.parent_name}</td>
                <td>${student.phone_number}</td>
                <td>
                    ${debt > 0
                        ? `<span class="text-danger fw-bold">${formatKuwaitiCurrency(debt)}</span>`
                        : debt < 0
                            ? `<span class="text-success fw-bold">${formatKuwaitiCurrency(Math.abs(debt))} (رصيد)</span>`
                            : `<span class="text-muted">0 فلس</span>`}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="student-details.html?id=${student.id}" class="btn btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="student-debt-management.html?id=${student.id}" class="btn btn-warning" data-bs-toggle="tooltip" title="إدارة الدين">
                            <i class="fas fa-hand-holding-usd"></i>
                        </a>
                        <a href="edit-student.html?id=${student.id}" class="btn btn-primary" data-bs-toggle="tooltip" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="new-transaction.html?student_id=${student.id}" class="btn btn-danger" data-bs-toggle="tooltip" title="تسجيل مشتريات">
                            <i class="fas fa-shopping-cart"></i>
                        </a>
                        <a href="payment.html?student_id=${student.id}" class="btn btn-success" data-bs-toggle="tooltip" title="تسجيل دفعة">
                            <i class="fas fa-money-bill-wave"></i>
                        </a>
                        <button class="btn btn-outline-danger delete-student-btn" data-student-id="${student.id}" data-bs-toggle="tooltip" title="حذف الطالب">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Add event listeners to delete buttons
        document.querySelectorAll('.delete-student-btn').forEach(button => {
            button.addEventListener('click', function() {
                const studentId = this.getAttribute('data-student-id');
                deleteStudent(studentId);
            });
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="7" class="text-center">
                لا يوجد طلاب مسجلين حالياً. <a href="add-student.html">إضافة طالب جديد</a>
            </td>
        `;
        tableBody.appendChild(row);
    }

    // Populate class filter
    const classFilter = document.getElementById('classFilter');
    classFilter.innerHTML = '<option value="">جميع الصفوف</option>';

    // Get unique classes
    const classes = [...new Set(students.map(student => student.class))].sort();

    classes.forEach(className => {
        const option = document.createElement('option');
        option.value = className;
        option.textContent = className;
        classFilter.appendChild(option);
    });
}

// Filter students based on search and filters
function filterStudents() {
    const searchValue = document.getElementById('studentSearch').value.toLowerCase();
    const selectedClass = document.getElementById('classFilter').value;
    const debtFilter = document.getElementById('debtFilter').value;
    const debtMin = document.getElementById('debtMin').value ? parseFloat(document.getElementById('debtMin').value) / 1000 : 0; // Convert from fils to KWD
    const debtMax = document.getElementById('debtMax').value ? parseFloat(document.getElementById('debtMax').value) / 1000 : Infinity; // Convert from fils to KWD

    const studentRows = document.querySelectorAll('.student-row');
    let visibleCount = 0;

    studentRows.forEach(row => {
        const studentName = row.querySelector('.student-name').textContent.toLowerCase();
        const studentId = row.querySelector('.student-id').textContent.toLowerCase();
        const studentClass = row.getAttribute('data-class');
        const studentDebt = parseFloat(row.getAttribute('data-debt'));

        // Check if matches search
        const matchesSearch = searchValue === '' ||
                             studentName.includes(searchValue) ||
                             studentId.includes(searchValue);

        // Check if matches class filter
        const matchesClass = selectedClass === '' || studentClass === selectedClass;

        // Check if matches debt filter
        let matchesDebtFilter = true;
        if (debtFilter === 'debt') {
            matchesDebtFilter = studentDebt > 0;
        } else if (debtFilter === 'no-debt') {
            matchesDebtFilter = studentDebt <= 0;
        }

        // Check if matches debt range
        const matchesDebtRange = studentDebt >= debtMin && studentDebt <= debtMax;

        // Show/hide row based on all filters
        if (matchesSearch && matchesClass && matchesDebtFilter && matchesDebtRange) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Show message if no results
    const tableBody = document.getElementById('students-table-body');
    const noResultsRow = document.getElementById('no-results-row');

    if (visibleCount === 0 && !noResultsRow) {
        const row = document.createElement('tr');
        row.id = 'no-results-row';
        row.innerHTML = `
            <td colspan="7" class="text-center">
                لا توجد نتائج مطابقة لمعايير البحث
            </td>
        `;
        tableBody.appendChild(row);
    } else if (visibleCount > 0 && noResultsRow) {
        noResultsRow.remove();
    }
}

// Reset all filters
function resetFilters() {
    document.getElementById('studentSearch').value = '';
    document.getElementById('classFilter').value = '';
    document.getElementById('debtFilter').value = 'all';
    document.getElementById('debtMin').value = '';
    document.getElementById('debtMax').value = '';

    filterStudents();
}

// Delete student function
function deleteStudent(studentId) {
    // Get data from localStorage
    let students = JSON.parse(localStorage.getItem('students')) || [];

    // Find student
    const student = students.find(student => student.id == studentId);

    if (!student) {
        alert('لم يتم العثور على الطالب!');
        return;
    }

    // First confirmation
    if (!confirm(`هل أنت متأكد من حذف الطالب "${student.name}" وجميع معاملاته؟`)) {
        return;
    }

    // Second confirmation for extra safety
    if (!confirm(`تأكيد نهائي: سيتم حذف الطالب "${student.name}" وجميع معاملاته بشكل نهائي ولا يمكن التراجع عن هذا الإجراء!`)) {
        return;
    }

    // Get transactions from localStorage
    let transactions = JSON.parse(localStorage.getItem('transactions')) || [];

    // Find student index
    const studentIndex = students.findIndex(student => student.id == studentId);

    if (studentIndex === -1) {
        alert('لم يتم العثور على الطالب!');
        return;
    }

    // Get student name for confirmation
    const studentName = students[studentIndex].name;

    // Remove student
    students.splice(studentIndex, 1);

    // Remove all transactions related to this student
    transactions = transactions.filter(transaction => transaction.student_id != studentId);

    // Remove all documents (invoices and receipts) related to this student
    let documents = JSON.parse(localStorage.getItem('documents')) || [];
    documents = documents.filter(document => document.student_id != studentId);

    // Save updated data to localStorage
    localStorage.setItem('students', JSON.stringify(students));
    localStorage.setItem('transactions', JSON.stringify(transactions));
    localStorage.setItem('documents', JSON.stringify(documents));

    // Show success message
    alert(`تم حذف الطالب "${studentName}" وجميع معاملاته بنجاح!`);

    // Reload students data
    loadStudentsData();
}
