// Custom JavaScript for the Canteen Debt Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Student search functionality
    const studentSearchInput = document.getElementById('studentSearch');
    if (studentSearchInput) {
        studentSearchInput.addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const studentRows = document.querySelectorAll('.student-row');
            
            studentRows.forEach(row => {
                const studentName = row.querySelector('.student-name').textContent.toLowerCase();
                const studentId = row.querySelector('.student-id').textContent.toLowerCase();
                
                if (studentName.includes(searchValue) || studentId.includes(searchValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Class filter functionality
    const classFilter = document.getElementById('classFilter');
    if (classFilter) {
        classFilter.addEventListener('change', function() {
            const selectedClass = this.value;
            const studentRows = document.querySelectorAll('.student-row');
            
            studentRows.forEach(row => {
                const studentClass = row.getAttribute('data-class');
                
                if (selectedClass === '' || studentClass === selectedClass) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Print report functionality
    const printReportBtn = document.getElementById('printReport');
    if (printReportBtn) {
        printReportBtn.addEventListener('click', function() {
            window.print();
        });
    }

    // Debt limit warning
    const debtAmountInputs = document.querySelectorAll('.debt-amount-input');
    if (debtAmountInputs.length > 0) {
        debtAmountInputs.forEach(input => {
            input.addEventListener('input', function() {
                const currentDebt = parseFloat(document.getElementById('currentDebt').value) || 0;
                const newAmount = parseFloat(this.value) || 0;
                const totalDebt = currentDebt + newAmount;
                const debtLimit = parseFloat(document.getElementById('debtLimit').value) || 100;
                
                const warningElement = document.getElementById('debtWarning');
                
                if (totalDebt > debtLimit) {
                    warningElement.textContent = 'تنبيه: سيتجاوز الدين الحد المسموح به!';
                    warningElement.classList.remove('d-none');
                } else {
                    warningElement.classList.add('d-none');
                }
            });
        });
    }

    // Confirm delete
    const deleteButtons = document.querySelectorAll('.delete-btn');
    if (deleteButtons.length > 0) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                    e.preventDefault();
                }
            });
        });
    }
});
