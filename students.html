<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="whatsapp-settings.html" class="list-group-item list-group-item-action">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة الطلاب</h2>
                    <a href="add-student.html" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة طالب جديد
                    </a>
                </div>

                <!-- Search and filter -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-filter me-2"></i> بحث وتصفية
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="studentSearch" class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" id="studentSearch" class="form-control" placeholder="ابحث باسم الطالب أو رقم الهوية...">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="classFilter" class="form-label">الصف</label>
                                <select id="classFilter" class="form-select">
                                    <option value="">جميع الصفوف</option>
                                    <!-- Will be populated by JavaScript -->
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6 mb-3 mb-md-0">
                                <label for="debtFilter" class="form-label">حالة الدين</label>
                                <select id="debtFilter" class="form-select">
                                    <option value="all">جميع الطلاب</option>
                                    <option value="debt">الطلاب المدينون فقط</option>
                                    <option value="no-debt">الطلاب بدون دين فقط</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">مدى الدين (بالفلس)</label>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="input-group">
                                            <span class="input-group-text">من</span>
                                            <input type="number" id="debtMin" class="form-control" min="0" step="1" placeholder="0">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="input-group">
                                            <span class="input-group-text">إلى</span>
                                            <input type="number" id="debtMax" class="form-control" min="0" step="1" placeholder="غير محدد">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button id="resetFilters" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i> إعادة تعيين
                            </button>
                            <button id="applyFilters" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i> تطبيق التصفية
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Students table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الصف</th>
                                        <th>رقم الهوية</th>
                                        <th>ولي الأمر</th>
                                        <th>رقم الجوال</th>
                                        <th>الدين</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="students-table-body">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/students.js"></script>
    <script src="js/import-data.js"></script>
</body>
</html>
