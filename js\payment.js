// Payment functionality

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Set default date to today
    document.getElementById('date').valueAsDate = new Date();

    // Load students with debt for dropdown
    loadStudentsWithDebt();

    // Check for student_id in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const studentId = urlParams.get('student_id');
    if (studentId) {
        document.getElementById('student_id').value = studentId;
        updateStudentDebtInfo(studentId);
    }

    // Add event listener for student selection change
    document.getElementById('student_id').addEventListener('change', function() {
        const selectedStudentId = this.value;
        if (selectedStudentId) {
            updateStudentDebtInfo(selectedStudentId);
        } else {
            document.getElementById('debt-info').classList.add('d-none');
        }
    });

    // Handle form submission
    document.getElementById('payment-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const studentId = document.getElementById('student_id').value;
        const amount = document.getElementById('amount').value;
        const description = document.getElementById('description').value;
        const date = document.getElementById('date').value;

        // Validate form data
        const errors = [];

        if (!studentId) {
            errors.push('يرجى اختيار الطالب');
        }

        if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
            errors.push('يرجى إدخال مبلغ صحيح');
        }

        if (!date) {
            errors.push('يرجى إدخال التاريخ');
        }

        // Display errors if any
        const errorAlert = document.getElementById('error-alert');
        const errorList = document.getElementById('error-list');

        if (errors.length > 0) {
            errorList.innerHTML = '';
            errors.forEach(error => {
                const li = document.createElement('li');
                li.textContent = error;
                errorList.appendChild(li);
            });
            errorAlert.classList.remove('d-none');
            return;
        }

        // Hide error alert if no errors
        errorAlert.classList.add('d-none');

        // Get existing transactions from localStorage
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

        // Generate new transaction ID
        const newId = transactions.length > 0 ? Math.max(...transactions.map(transaction => transaction.id)) + 1 : 1;

        // Create new transaction object
        const newTransaction = {
            id: newId,
            student_id: parseInt(studentId),
            type: 'payment',
            amount: parseFloat(amount) / 1000, // تحويل من فلس إلى دينار
            description: description,
            date: date,
            created_by: currentUser.id,
            created_at: new Date().toISOString()
        };

        // Add new transaction to array
        transactions.push(newTransaction);

        // Save updated transactions array to localStorage
        localStorage.setItem('transactions', JSON.stringify(transactions));

        // Show success message and redirect
        alert('تم تسجيل الدفعة بنجاح');
        window.location.href = 'student-details.html?id=' + studentId;
    });
});

// Load students with debt for dropdown
function loadStudentsWithDebt() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const studentSelect = document.getElementById('student_id');

    // Calculate student debts
    const studentDebts = {};

    // Initialize student debts
    students.forEach(student => {
        studentDebts[student.id] = {
            id: student.id,
            name: student.name,
            class: student.class,
            debt: 0
        };
    });

    // Calculate debts from transactions
    transactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            studentDebts[transaction.student_id].debt += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            studentDebts[transaction.student_id].debt -= parseFloat(transaction.amount);
        }
    });

    // Convert to array and filter students with debt
    const studentsWithDebt = Object.values(studentDebts)
        .filter(student => student.debt > 0)
        .sort((a, b) => b.debt - a.debt);

    if (studentsWithDebt.length > 0) {
        studentsWithDebt.forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.name} - ${student.class} (الدين: ${student.debt.toFixed(2)} دينار)`;
            studentSelect.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.disabled = true;
        option.textContent = 'لا يوجد طلاب مدينين';
        studentSelect.appendChild(option);
    }
}

// Update student debt information
function updateStudentDebtInfo(studentId) {
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    let currentDebt = 0;

    // Calculate current debt
    transactions.forEach(transaction => {
        if (transaction.student_id == studentId) {
            if (transaction.type === 'purchase') {
                currentDebt += parseFloat(transaction.amount);
            } else if (transaction.type === 'payment') {
                currentDebt -= parseFloat(transaction.amount);
            }
        }
    });

    // Update debt info display
    document.getElementById('current-debt').textContent = currentDebt.toFixed(2);
    document.getElementById('debt-info').classList.remove('d-none');

    // Set default payment amount to current debt if positive (convert to fils)
    if (currentDebt > 0) {
        document.getElementById('amount').value = Math.round(currentDebt * 1000);
    }
}
