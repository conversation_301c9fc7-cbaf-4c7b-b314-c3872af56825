<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Get student ID from URL if provided
$student_id = $_GET['student_id'] ?? null;
$student = null;

// If student ID is provided, get student details
if ($student_id) {
    $stmt = $pdo->prepare('SELECT * FROM students WHERE id = ?');
    $stmt->execute([$student_id]);
    $student = $stmt->fetch();
    
    if ($student) {
        // Get student debt
        $stmt = $pdo->prepare('SELECT 
            COALESCE(SUM(CASE WHEN type = "purchase" THEN amount ELSE 0 END), 0) - 
            COALESCE(SUM(CASE WHEN type = "payment" THEN amount ELSE 0 END), 0) as debt
            FROM transactions
            WHERE student_id = ?');
        $stmt->execute([$student_id]);
        $debt_info = $stmt->fetch();
        $current_debt = $debt_info['debt'] ?? 0;
    }
}

// Get all students for dropdown
$stmt = $pdo->query('SELECT id, name, class FROM students ORDER BY name');
$all_students = $stmt->fetchAll();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $student_id = $_POST['student_id'] ?? '';
    $amount = $_POST['amount'] ?? '';
    $description = $_POST['description'] ?? '';
    $date = $_POST['date'] ?? date('Y-m-d');
    
    // Validate form data
    $errors = [];
    
    if (empty($student_id)) {
        $errors[] = 'يرجى اختيار الطالب';
    }
    
    if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
        $errors[] = 'يرجى إدخال مبلغ صحيح';
    }
    
    if (empty($date)) {
        $errors[] = 'يرجى إدخال التاريخ';
    }
    
    // If no errors, insert transaction
    if (empty($errors)) {
        $stmt = $pdo->prepare('INSERT INTO transactions (student_id, type, amount, description, date, created_by, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())');
        $result = $stmt->execute([$student_id, 'purchase', $amount, $description, $date, getCurrentUserId()]);
        
        if ($result) {
            // Set success message
            $_SESSION['flash_message'] = 'تم تسجيل المشتريات بنجاح';
            $_SESSION['flash_type'] = 'success';
            
            // Redirect to student view
            header('Location: ../students/view.php?id=' . $student_id);
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء تسجيل المشتريات';
        }
    }
}

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>تسجيل مشتريات</h2>
            <a href="<?php echo $student ? '../students/view.php?id=' . $student['id'] : '../dashboard.php'; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة
            </a>
        </div>
        
        <!-- Display errors if any -->
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- New transaction form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_id" class="form-label">الطالب <span class="text-danger">*</span></label>
                            <?php if ($student): ?>
                                <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($student['name'] . ' - ' . $student['class']); ?>" readonly>
                            <?php else: ?>
                                <select class="form-select" id="student_id" name="student_id" required>
                                    <option value="">اختر الطالب...</option>
                                    <?php foreach ($all_students as $s): ?>
                                        <option value="<?php echo $s['id']; ?>" <?php echo (isset($_POST['student_id']) && $_POST['student_id'] == $s['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($s['name'] . ' - ' . $s['class']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo htmlspecialchars($_POST['date'] ?? date('Y-m-d')); ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="amount" class="form-label">المبلغ (ريال) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control debt-amount-input" id="amount" name="amount" step="0.01" min="0.01" value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>" required>
                            <?php if (isset($current_debt) && $current_debt > 0): ?>
                                <input type="hidden" id="currentDebt" value="<?php echo $current_debt; ?>">
                                <input type="hidden" id="debtLimit" value="100">
                                <div id="debtWarning" class="text-danger mt-2 d-none"></div>
                                <div class="form-text">الدين الحالي: <?php echo number_format($current_debt, 2); ?> ريال</div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <input type="text" class="form-control" id="description" name="description" value="<?php echo htmlspecialchars($_POST['description'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                        <button type="submit" class="btn btn-primary">تسجيل المشتريات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
