<div class="col-md-3">
    <div class="card">
        <div class="card-header bg-primary text-white">
            القائمة الرئيسية
        </div>
        <div class="list-group list-group-flush">
            <a href="/dashboard.php" class="list-group-item list-group-item-action <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
            </a>
            <a href="/students/list.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/students/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
            </a>
            <a href="/transactions/new.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/transactions/new.php' ? 'active' : ''; ?>">
                <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
            </a>
            <a href="/transactions/payment.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/transactions/payment.php' ? 'active' : ''; ?>">
                <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
            </a>
            <a href="/transactions/history.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/transactions/history.php' ? 'active' : ''; ?>">
                <i class="fas fa-history me-2"></i> سجل المعاملات
            </a>
            <a href="/reports/generate.php" class="list-group-item list-group-item-action <?php echo strpos($_SERVER['PHP_SELF'], '/reports/') !== false ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar me-2"></i> التقارير
            </a>
            <a href="/reports/notifications.php" class="list-group-item list-group-item-action <?php echo $_SERVER['PHP_SELF'] == '/reports/notifications.php' ? 'active' : ''; ?>">
                <i class="fas fa-bell me-2"></i> الإشعارات
            </a>
        </div>
    </div>
</div>
