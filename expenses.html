<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصروفات المقصف - نظام إدارة ديون المقصف المدرسي</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">نظام إدارة ديون المقصف</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.html">الطلاب</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-transaction.html">تسجيل مشتريات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="payment.html">تسجيل دفعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="expenses.html">مصروفات المقصف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">التقارير</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <span id="username">المستخدم</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" id="logout-btn">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div class="card sidebar">
                    <div class="card-header bg-primary text-white">
                        القائمة الرئيسية
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="dashboard.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                        <a href="students.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-graduate me-2"></i> إدارة الطلاب
                        </a>
                        <a href="new-transaction.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-shopping-cart me-2"></i> تسجيل مشتريات
                        </a>
                        <a href="payment.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2"></i> تسجيل دفعات
                        </a>
                        <a href="expenses.html" class="list-group-item list-group-item-action active">
                            <i class="fas fa-file-invoice-dollar me-2"></i> مصروفات المقصف
                        </a>
                        <a href="transactions.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-history me-2"></i> سجل المعاملات
                        </a>
                        <a href="reports.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2"></i> التقارير
                        </a>
                        <a href="notifications.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-bell me-2"></i> الإشعارات
                        </a>
                        <a href="whatsapp-settings.html" class="list-group-item list-group-item-action">
                            <i class="fab fa-whatsapp me-2"></i> إعدادات الواتساب
                        </a>
                        <a href="system-settings.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-cog me-2"></i> إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>مصروفات المقصف</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                        <i class="fas fa-plus-circle me-1"></i> إضافة مصروف جديد
                    </button>
                </div>

                <!-- Expense summary -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي المصروفات</h5>
                                <h3 class="card-text" id="total-expenses">0.000 د.ك</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">مصروفات هذا الشهر</h5>
                                <h3 class="card-text" id="month-expenses">0.000 د.ك</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">عدد المصروفات</h5>
                                <h3 class="card-text" id="expense-count">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expense filters -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <i class="fas fa-filter me-2"></i> تصفية المصروفات
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="expense-type-filter" class="form-label">نوع المصروف</label>
                                <select class="form-select" id="expense-type-filter">
                                    <option value="">جميع الأنواع</option>
                                    <!-- Will be populated by JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="date-from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date-from">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="date-to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date-to">
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button id="reset-filters" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i> إعادة تعيين
                            </button>
                            <button id="apply-filters" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i> تطبيق التصفية
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Expenses table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>نوع المصروف</th>
                                        <th>الوصف</th>
                                        <th>المبلغ</th>
                                        <th>المستخدم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="expenses-table-body">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Expense Modal -->
    <div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addExpenseModalLabel">إضافة مصروف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-expense-form">
                        <div class="mb-3">
                            <label for="expense-type" class="form-label">نوع المصروف <span class="text-danger">*</span></label>
                            <select class="form-select" id="expense-type" required>
                                <option value="">اختر نوع المصروف...</option>
                                <option value="مشتريات">مشتريات</option>
                                <option value="رواتب">رواتب</option>
                                <option value="صيانة">صيانة</option>
                                <option value="فواتير">فواتير</option>
                                <option value="إيجار">إيجار</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3" id="other-type-container" style="display: none;">
                            <label for="other-type" class="form-label">نوع المصروف (آخر) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="other-type" placeholder="أدخل نوع المصروف...">
                        </div>
                        <div class="mb-3">
                            <label for="expense-date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="expense-date" required>
                        </div>
                        <div class="mb-3">
                            <label for="expense-amount" class="form-label">المبلغ (فلس) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="expense-amount" min="1" step="1" required>
                            <div class="form-text">أدخل المبلغ بالفلس (1000 فلس = 1 دينار)</div>
                        </div>
                        <div class="mb-3">
                            <label for="expense-description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="expense-description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-expense-btn">حفظ المصروف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Expense Modal -->
    <div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editExpenseModalLabel">تعديل المصروف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-expense-form">
                        <input type="hidden" id="edit-expense-id">
                        <div class="mb-3">
                            <label for="edit-expense-type" class="form-label">نوع المصروف <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit-expense-type" required>
                                <option value="">اختر نوع المصروف...</option>
                                <option value="مشتريات">مشتريات</option>
                                <option value="رواتب">رواتب</option>
                                <option value="صيانة">صيانة</option>
                                <option value="فواتير">فواتير</option>
                                <option value="إيجار">إيجار</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3" id="edit-other-type-container" style="display: none;">
                            <label for="edit-other-type" class="form-label">نوع المصروف (آخر) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit-other-type" placeholder="أدخل نوع المصروف...">
                        </div>
                        <div class="mb-3">
                            <label for="edit-expense-date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="edit-expense-date" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit-expense-amount" class="form-label">المبلغ (فلس) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit-expense-amount" min="1" step="1" required>
                            <div class="form-text">أدخل المبلغ بالفلس (1000 فلس = 1 دينار)</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit-expense-description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="edit-expense-description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="update-expense-btn">تحديث المصروف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">&copy; 2023 نظام إدارة ديون المقصف المدرسي</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/auth.js"></script>
    <script src="js/expenses.js"></script>
</body>
</html>
