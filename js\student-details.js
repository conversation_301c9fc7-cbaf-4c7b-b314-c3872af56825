// Student details functionality

// Global variables
let currentUser;

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Get student ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const studentId = urlParams.get('id');

    if (!studentId) {
        alert('معرف الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }

    // Load student data
    loadStudentData(studentId);

    // Setup action buttons
    document.getElementById('manage-debt-btn').href = `student-debt-management.html?id=${studentId}`;
    document.getElementById('create-installment-btn').addEventListener('click', function(e) {
        e.preventDefault();
        window.location.href = `installments.html?student_id=${studentId}`;
    });
    document.getElementById('new-purchase-btn').href = `new-transaction.html?student_id=${studentId}`;
    document.getElementById('new-payment-btn').href = `payment.html?student_id=${studentId}`;
    document.getElementById('edit-student-btn').href = `edit-student.html?id=${studentId}`;
    document.getElementById('generate-report-btn').href = `reports.html?student_id=${studentId}`;
    document.getElementById('send-notification-btn').href = `notifications.html?student_id=${studentId}`;
    document.getElementById('send-whatsapp-btn').addEventListener('click', function(e) {
        e.preventDefault();
        openWhatsAppModal(studentId);
    });

    // Setup print functionality
    setupPrintFunctionality();
});

// Load student data
function loadStudentData(studentId) {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];

    // Find student by ID
    const student = students.find(s => s.id == studentId);

    if (!student) {
        alert('الطالب غير موجود');
        window.location.href = 'students.html';
        return;
    }

    // Display student information
    document.getElementById('student-name').textContent = student.name;
    document.getElementById('student-class').textContent = student.class;
    document.getElementById('student-id').textContent = student.student_id;
    document.getElementById('parent-name').textContent = student.parent_name;
    document.getElementById('phone-number').textContent = student.phone_number;
    document.getElementById('created-at').textContent = formatDate(student.created_at);

    // Calculate account summary
    let totalPurchases = 0;
    let totalPayments = 0;

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == studentId);

    studentTransactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            totalPurchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            totalPayments += parseFloat(transaction.amount);
        }
    });

    const balance = totalPurchases - totalPayments;

    // Update account summary
    document.getElementById('total-purchases').textContent = totalPurchases.toFixed(2) + ' دينار';
    document.getElementById('total-payments').textContent = totalPayments.toFixed(2) + ' دينار';

    const balanceElement = document.getElementById('current-balance');
    const balanceStatusElement = document.getElementById('balance-status');

    if (balance > 0) {
        balanceElement.textContent = balance.toFixed(2) + ' دينار';
        balanceElement.className = 'text-danger';
        balanceStatusElement.textContent = 'مدين';
        balanceStatusElement.className = 'text-danger';
    } else if (balance < 0) {
        balanceElement.textContent = Math.abs(balance).toFixed(2) + ' دينار';
        balanceElement.className = 'text-success';
        balanceStatusElement.textContent = 'دائن (رصيد متبقي)';
        balanceStatusElement.className = 'text-success';
    } else {
        balanceElement.textContent = '0.00 دينار';
        balanceElement.className = '';
        balanceStatusElement.textContent = 'متعادل';
        balanceStatusElement.className = 'text-muted';
    }

    // Display transaction history
    const tableBody = document.getElementById('transactions-table-body');
    tableBody.innerHTML = '';

    if (studentTransactions.length > 0) {
        // Sort transactions by date (newest first)
        studentTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        studentTransactions.forEach(transaction => {
            const user = users.find(u => u.id === transaction.created_by);
            const userName = user ? user.username : 'غير معروف';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formatDate(transaction.date)}</td>
                <td>
                    ${transaction.type === 'purchase'
                        ? '<span class="badge bg-danger">مشتريات</span>'
                        : '<span class="badge bg-success">دفعة</span>'}
                </td>
                <td>
                    ${transaction.type === 'purchase'
                        ? `<span class="text-danger">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`
                        : `<span class="text-success">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`}
                </td>
                <td>${transaction.description || '-'}</td>
                <td>${userName}</td>
                <td>
                    <button class="btn btn-sm btn-outline-secondary print-receipt-btn" data-transaction-id="${transaction.id}" data-transaction-type="${transaction.type}">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" class="text-center">
                لا توجد معاملات مسجلة لهذا الطالب.
            </td>
        `;
        tableBody.appendChild(row);
    }
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

// Format Kuwaiti currency
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return new Intl.NumberFormat('ar-KW', {
            style: 'currency',
            currency: 'KWD',
            minimumFractionDigits: 3
        }).format(amount);
    }
}

// Open WhatsApp modal
function openWhatsAppModal(studentId) {
    // Get data from localStorage
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const whatsappSettings = JSON.parse(localStorage.getItem('whatsappSettings')) || {
        enabled: true,
        defaultCountryCode: '+965',
        templates: {
            debtNotification: 'مرحباً {parent_name}،\nنود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.\nنرجو سداد المبلغ في أقرب وقت ممكن.\nمع الشكر، إدارة المدرسة',
            paymentConfirmation: 'مرحباً {parent_name}،\nنشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.\nالرصيد المتبقي: {remaining_debt}\nمع الشكر، إدارة المدرسة',
            customMessage: 'مرحباً {parent_name}،\n{message}\nمع الشكر، إدارة المدرسة'
        }
    };

    // Find student by ID
    const student = students.find(s => s.id == studentId);

    if (!student) {
        alert('الطالب غير موجود');
        return;
    }

    // Check if WhatsApp is enabled
    if (!whatsappSettings.enabled) {
        alert('خدمة الواتساب غير مفعلة. يرجى تفعيلها من إعدادات النظام.');
        return;
    }

    // Check if WhatsApp is connected
    const whatsappConnection = JSON.parse(localStorage.getItem('whatsappConnection')) || { status: 'disconnected' };

    if (whatsappConnection.status !== 'connected') {
        alert('الواتساب غير متصل. يرجى الاتصال بالواتساب من صفحة إعدادات الواتساب.');
        window.location.href = 'whatsapp-settings.html';
        return;
    }

    // Check if student has phone number
    if (!student.phone_number) {
        alert('الطالب ليس لديه رقم هاتف مسجل.');
        return;
    }

    // Calculate account summary for debt message
    let totalPurchases = 0;
    let totalPayments = 0;

    // Filter transactions for this student
    const studentTransactions = transactions.filter(t => t.student_id == studentId);

    studentTransactions.forEach(transaction => {
        if (transaction.type === 'purchase') {
            totalPurchases += parseFloat(transaction.amount);
        } else if (transaction.type === 'payment') {
            totalPayments += parseFloat(transaction.amount);
        }
    });

    const balance = totalPurchases - totalPayments;

    // Format phone number
    let phoneNumber = student.phone_number;

    // Remove any non-digit characters
    phoneNumber = phoneNumber.replace(/\D/g, '');

    // Add country code if not present
    if (!phoneNumber.startsWith('+')) {
        phoneNumber = whatsappSettings.defaultCountryCode + phoneNumber;
    }

    // Set recipient in modal
    document.getElementById('whatsapp-recipient').value = `${student.parent_name} (${phoneNumber})`;

    // Setup message type change event
    document.getElementById('whatsapp-message-type').addEventListener('change', function() {
        updateWhatsAppPreview(student, balance);
    });

    // Setup custom message change event
    document.getElementById('whatsapp-custom-message').addEventListener('input', function() {
        updateWhatsAppPreview(student, balance);
    });

    // Initial preview update
    updateWhatsAppPreview(student, balance);

    // Setup send buttons
    document.getElementById('send-whatsapp-message-btn').onclick = function() {
        sendWhatsAppMessage(student, balance);
    };

    document.getElementById('open-whatsapp-direct-btn').onclick = function() {
        openWhatsAppDirect(student, balance);
    };

    // Show modal
    const whatsappModal = new bootstrap.Modal(document.getElementById('whatsappModal'));
    whatsappModal.show();
}

// Update WhatsApp preview
function updateWhatsAppPreview(student, balance) {
    const messageType = document.getElementById('whatsapp-message-type').value;
    const customMessage = document.getElementById('whatsapp-custom-message').value;
    const customMessageContainer = document.getElementById('whatsapp-custom-message-container');

    // Show/hide custom message container
    if (messageType === 'custom') {
        customMessageContainer.style.display = 'block';
    } else {
        customMessageContainer.style.display = 'none';
    }

    // Get WhatsApp settings
    const whatsappSettings = JSON.parse(localStorage.getItem('whatsappSettings')) || {
        templates: {
            debtNotification: 'مرحباً {parent_name}،\nنود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.\nنرجو سداد المبلغ في أقرب وقت ممكن.\nمع الشكر، إدارة المدرسة',
            paymentConfirmation: 'مرحباً {parent_name}،\nنشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.\nالرصيد المتبقي: {remaining_debt}\nمع الشكر، إدارة المدرسة',
            customMessage: 'مرحباً {parent_name}،\n{message}\nمع الشكر، إدارة المدرسة'
        }
    };

    // Get message text
    let messageText = '';

    if (messageType === 'debt') {
        messageText = whatsappSettings.templates.debtNotification
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{student_class}', student.class)
            .replace('{debt_amount}', formatKuwaitiCurrency(balance))
            .replace('{school_name}', 'المدرسة النموذجية');
    } else if (messageType === 'payment') {
        // For payment confirmation, we'll use the last payment as an example
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
        const payments = transactions.filter(t => t.student_id == student.id && t.type === 'payment');

        // Sort payments by date (newest first)
        payments.sort((a, b) => new Date(b.date) - new Date(a.date));

        const lastPayment = payments.length > 0 ? payments[0] : { amount: 0, date: new Date() };

        messageText = whatsappSettings.templates.paymentConfirmation
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{payment_amount}', formatKuwaitiCurrency(parseFloat(lastPayment.amount)))
            .replace('{payment_date}', formatDate(lastPayment.date))
            .replace('{remaining_debt}', formatKuwaitiCurrency(balance));
    } else if (messageType === 'custom') {
        messageText = whatsappSettings.templates.customMessage
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{message}', customMessage)
            .replace('{school_name}', 'المدرسة النموذجية');
    }

    // Update preview
    document.getElementById('whatsapp-preview').textContent = messageText;
}

// Open WhatsApp directly
function openWhatsAppDirect(student, balance) {
    const messageType = document.getElementById('whatsapp-message-type').value;
    const customMessage = document.getElementById('whatsapp-custom-message').value;

    // Get WhatsApp settings
    const whatsappSettings = JSON.parse(localStorage.getItem('whatsappSettings')) || {
        defaultCountryCode: '+965',
        templates: {
            debtNotification: 'مرحباً {parent_name}،\nنود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.\nنرجو سداد المبلغ في أقرب وقت ممكن.\nمع الشكر، إدارة المدرسة',
            paymentConfirmation: 'مرحباً {parent_name}،\nنشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.\nالرصيد المتبقي: {remaining_debt}\nمع الشكر، إدارة المدرسة',
            customMessage: 'مرحباً {parent_name}،\n{message}\nمع الشكر، إدارة المدرسة'
        }
    };

    // Format phone number
    let phoneNumber = student.phone_number;

    // Remove any non-digit characters
    phoneNumber = phoneNumber.replace(/\D/g, '');

    // Add country code if not present
    if (!phoneNumber.startsWith('+')) {
        phoneNumber = whatsappSettings.defaultCountryCode + phoneNumber;
    }

    // Get message text
    let messageText = '';

    if (messageType === 'debt') {
        messageText = whatsappSettings.templates.debtNotification
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{student_class}', student.class)
            .replace('{debt_amount}', formatKuwaitiCurrency(balance))
            .replace('{school_name}', 'المدرسة النموذجية');
    } else if (messageType === 'payment') {
        // For payment confirmation, we'll use the last payment as an example
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
        const payments = transactions.filter(t => t.student_id == student.id && t.type === 'payment');

        // Sort payments by date (newest first)
        payments.sort((a, b) => new Date(b.date) - new Date(a.date));

        const lastPayment = payments.length > 0 ? payments[0] : { amount: 0, date: new Date() };

        messageText = whatsappSettings.templates.paymentConfirmation
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{payment_amount}', formatKuwaitiCurrency(parseFloat(lastPayment.amount)))
            .replace('{payment_date}', formatDate(lastPayment.date))
            .replace('{remaining_debt}', formatKuwaitiCurrency(balance));
    } else if (messageType === 'custom') {
        messageText = whatsappSettings.templates.customMessage
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{message}', customMessage)
            .replace('{school_name}', 'المدرسة النموذجية');
    }

    // Close modal
    const whatsappModal = bootstrap.Modal.getInstance(document.getElementById('whatsappModal'));
    whatsappModal.hide();

    try {
        // Format phone number for WhatsApp API
        let waPhoneNumber = phoneNumber;

        // Remove any non-digit characters
        waPhoneNumber = waPhoneNumber.replace(/\D/g, '');

        // Make sure it doesn't start with +
        if (waPhoneNumber.startsWith('+')) {
            waPhoneNumber = waPhoneNumber.substring(1);
        }

        console.log('Formatted phone number for WhatsApp:', waPhoneNumber);

        // Encode message for URL
        const encodedMessage = encodeURIComponent(messageText);

        // Create WhatsApp URL
        const whatsappUrl = `https://wa.me/${waPhoneNumber}?text=${encodedMessage}`;
        console.log('WhatsApp URL:', whatsappUrl);

        // Open WhatsApp in new tab
        const newWindow = window.open(whatsappUrl, '_blank');

        if (!newWindow) {
            // If popup was blocked
            alert('تم منع فتح نافذة جديدة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.');
        } else {
            // Show success message
            alert('تم فتح الواتساب لإرسال الرسالة.');
        }
    } catch (error) {
        console.error('Error opening WhatsApp URL:', error);
        alert('حدث خطأ أثناء محاولة فتح الواتساب: ' + error.message);
    }
}

// Send WhatsApp message
async function sendWhatsAppMessage(student, balance) {
    const messageType = document.getElementById('whatsapp-message-type').value;
    const customMessage = document.getElementById('whatsapp-custom-message').value;

    // Get WhatsApp settings
    const whatsappSettings = JSON.parse(localStorage.getItem('whatsappSettings')) || {
        defaultCountryCode: '+965',
        templates: {
            debtNotification: 'مرحباً {parent_name}،\nنود إعلامكم بأن ابنكم/ابنتكم {student_name} لديه دين في المقصف المدرسي بقيمة {debt_amount}.\nنرجو سداد المبلغ في أقرب وقت ممكن.\nمع الشكر، إدارة المدرسة',
            paymentConfirmation: 'مرحباً {parent_name}،\nنشكركم على سداد مبلغ {payment_amount} من دين المقصف المدرسي للطالب/ة {student_name}.\nالرصيد المتبقي: {remaining_debt}\nمع الشكر، إدارة المدرسة',
            customMessage: 'مرحباً {parent_name}،\n{message}\nمع الشكر، إدارة المدرسة'
        }
    };

    // Format phone number
    let phoneNumber = student.phone_number;

    // Remove any non-digit characters
    phoneNumber = phoneNumber.replace(/\D/g, '');

    // Add country code if not present
    if (!phoneNumber.startsWith('+')) {
        phoneNumber = whatsappSettings.defaultCountryCode + phoneNumber;
    }

    // Get message text
    let messageText = '';

    if (messageType === 'debt') {
        messageText = whatsappSettings.templates.debtNotification
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{student_class}', student.class)
            .replace('{debt_amount}', formatKuwaitiCurrency(balance))
            .replace('{school_name}', 'المدرسة النموذجية');
    } else if (messageType === 'payment') {
        // For payment confirmation, we'll use the last payment as an example
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
        const payments = transactions.filter(t => t.student_id == student.id && t.type === 'payment');

        // Sort payments by date (newest first)
        payments.sort((a, b) => new Date(b.date) - new Date(a.date));

        const lastPayment = payments.length > 0 ? payments[0] : { amount: 0, date: new Date() };

        messageText = whatsappSettings.templates.paymentConfirmation
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{payment_amount}', formatKuwaitiCurrency(parseFloat(lastPayment.amount)))
            .replace('{payment_date}', formatDate(lastPayment.date))
            .replace('{remaining_debt}', formatKuwaitiCurrency(balance));
    } else if (messageType === 'custom') {
        messageText = whatsappSettings.templates.customMessage
            .replace('{parent_name}', student.parent_name)
            .replace('{student_name}', student.name)
            .replace('{message}', customMessage)
            .replace('{school_name}', 'المدرسة النموذجية');
    }

    // Close modal
    const whatsappModal = bootstrap.Modal.getInstance(document.getElementById('whatsappModal'));
    whatsappModal.hide();

    // Check if TwilioIntegration is available and configured
    if (window.TwilioIntegration &&
        TwilioIntegration.config.accountSid &&
        TwilioIntegration.config.accountSid !== 'YOUR_ACCOUNT_SID') {

        // Use Twilio to send the message
        try {
            // Show loading message
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'position-fixed top-50 start-50 translate-middle bg-white p-3 rounded shadow';
            loadingMessage.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border text-primary me-3" role="status">
                        <span class="visually-hidden">جاري الإرسال...</span>
                    </div>
                    <div>جاري إرسال رسالة الواتساب...</div>
                </div>
            `;
            document.body.appendChild(loadingMessage);

            // Send message using TwilioIntegration
            const result = await TwilioIntegration.sendMessage(phoneNumber, messageText);

            // Remove loading message
            document.body.removeChild(loadingMessage);

            // Show success message
            alert('تم إرسال رسالة الواتساب بنجاح.');

        } catch (error) {
            // Show error message
            alert('حدث خطأ أثناء إرسال رسالة الواتساب: ' + error.message);
        }
    } else {
        // Fallback to direct WhatsApp URL
        try {
            // Format phone number for WhatsApp API
            let waPhoneNumber = phoneNumber;

            // Remove any non-digit characters
            waPhoneNumber = waPhoneNumber.replace(/\D/g, '');

            // Make sure it doesn't start with +
            if (waPhoneNumber.startsWith('+')) {
                waPhoneNumber = waPhoneNumber.substring(1);
            }

            console.log('Formatted phone number for WhatsApp:', waPhoneNumber);

            // Encode message for URL
            const encodedMessage = encodeURIComponent(messageText);

            // Create WhatsApp URL
            const whatsappUrl = `https://wa.me/${waPhoneNumber}?text=${encodedMessage}`;
            console.log('WhatsApp URL:', whatsappUrl);

            // Open WhatsApp in new tab
            const newWindow = window.open(whatsappUrl, '_blank');

            if (!newWindow) {
                // If popup was blocked
                alert('تم منع فتح نافذة جديدة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.');
            } else {
                // Show success message
                alert('تم فتح الواتساب لإرسال الرسالة.');
            }
        } catch (error) {
            console.error('Error opening WhatsApp URL:', error);
            alert('حدث خطأ أثناء محاولة فتح الواتساب: ' + error.message);
        }
    }
}

// Setup print functionality
function setupPrintFunctionality() {
    // Add main print button event listener
    document.getElementById('print-btn').addEventListener('click', function() {
        // Get student data
        const urlParams = new URLSearchParams(window.location.search);
        const studentId = urlParams.get('id');

        if (!studentId) {
            alert('معرف الطالب غير موجود');
            return;
        }

        // Get data from localStorage
        const students = JSON.parse(localStorage.getItem('students')) || [];
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

        // Find student by ID
        const student = students.find(s => s.id == studentId);

        if (!student) {
            alert('الطالب غير موجود');
            return;
        }

        // Print student details report
        printStudentDetails(student, transactions);
    });

    // Add event listeners for receipt print buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.print-receipt-btn')) {
            const button = e.target.closest('.print-receipt-btn');
            const transactionId = button.getAttribute('data-transaction-id');
            const transactionType = button.getAttribute('data-transaction-type');

            // Get student data
            const urlParams = new URLSearchParams(window.location.search);
            const studentId = urlParams.get('id');

            if (!studentId || !transactionId) {
                alert('بيانات غير مكتملة');
                return;
            }

            // Get data from localStorage
            const students = JSON.parse(localStorage.getItem('students')) || [];
            const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

            // Find student and transaction
            const student = students.find(s => s.id == studentId);
            const transaction = transactions.find(t => t.id == transactionId);

            if (!student || !transaction) {
                alert('لم يتم العثور على البيانات');
                return;
            }

            // Print appropriate receipt based on transaction type
            if (transactionType === 'purchase') {
                printPurchaseInvoice(transaction, student);
            } else if (transactionType === 'payment') {
                printPaymentReceipt(transaction, student);
            }
        }
    });
}
