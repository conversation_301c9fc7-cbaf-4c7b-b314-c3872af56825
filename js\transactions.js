// Transactions history functionality

// Global variables
let currentUser;

// Currency format - <PERSON>i <PERSON>ls
const currencyFormat = new Intl.NumberFormat('ar-KW', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 3
});

// Format amount in Fils/Dinar
function formatKuwaitiCurrency(amount) {
    // Convert to number to ensure proper calculation
    amount = parseFloat(amount);

    // If amount is less than 1 KWD, show in fils
    if (Math.abs(amount) < 1) {
        // Convert to fils (1 KWD = 1000 fils)
        const fils = Math.round(amount * 1000);
        return `${fils} فلس`;
    } else {
        // Show in KWD using the currency formatter
        return currencyFormat.format(amount);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set username in navbar
    currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
    if (currentUser) {
        document.getElementById('username').textContent = currentUser.username;
    }

    // Handle logout
    document.getElementById('logout-btn').addEventListener('click', function(e) {
        e.preventDefault();
        sessionStorage.removeItem('currentUser');
        window.location.href = 'index.html';
    });

    // Load students for dropdown
    loadStudents();

    // Handle form submission
    document.getElementById('transaction-filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        loadTransactions();
    });

    // Handle reset button
    document.getElementById('reset-filter').addEventListener('click', function() {
        document.getElementById('student_id').value = '';
        document.getElementById('type').value = '';
        document.getElementById('date_from').value = '';
        document.getElementById('date_to').value = '';
        loadTransactions();
    });

    // Load initial transactions
    loadTransactions();

    // Setup print functionality
    setupPrintFunctionality();
});

// Load students for dropdown
function loadStudents() {
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const studentSelect = document.getElementById('student_id');

    if (students.length > 0) {
        students.sort((a, b) => a.name.localeCompare(b.name)).forEach(student => {
            const option = document.createElement('option');
            option.value = student.id;
            option.textContent = `${student.name} - ${student.class}`;
            studentSelect.appendChild(option);
        });
    } else {
        const option = document.createElement('option');
        option.disabled = true;
        option.textContent = 'لا يوجد طلاب مسجلين';
        studentSelect.appendChild(option);
    }
}

// Load transactions based on filters
function loadTransactions() {
    // Get filter values
    const studentId = document.getElementById('student_id').value;
    const type = document.getElementById('type').value;
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;

    // Get data from localStorage
    const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
    const students = JSON.parse(localStorage.getItem('students')) || [];
    const users = JSON.parse(localStorage.getItem('users')) || [];

    // Filter transactions
    let filteredTransactions = [...transactions];

    if (studentId) {
        filteredTransactions = filteredTransactions.filter(t => t.student_id == studentId);
    }

    if (type) {
        filteredTransactions = filteredTransactions.filter(t => t.type === type);
    }

    if (dateFrom) {
        filteredTransactions = filteredTransactions.filter(t => t.date >= dateFrom);
    }

    if (dateTo) {
        filteredTransactions = filteredTransactions.filter(t => t.date <= dateTo);
    }

    // Sort transactions by date (newest first)
    filteredTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // Display transactions
    const tableBody = document.getElementById('transactions-table-body');
    tableBody.innerHTML = '';

    let totalPurchases = 0;
    let totalPayments = 0;

    if (filteredTransactions.length > 0) {
        filteredTransactions.forEach(transaction => {
            // Update totals
            if (transaction.type === 'purchase') {
                totalPurchases += parseFloat(transaction.amount);
            } else if (transaction.type === 'payment') {
                totalPayments += parseFloat(transaction.amount);
            }

            const student = students.find(s => s.id === transaction.student_id);
            const studentName = student ? `${student.name} - ${student.class}` : 'غير معروف';

            const user = users.find(u => u.id === transaction.created_by);
            const userName = user ? user.username : 'غير معروف';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formatDate(transaction.date)}</td>
                <td>
                    <a href="student-details.html?id=${transaction.student_id}">
                        ${studentName}
                    </a>
                </td>
                <td>
                    ${transaction.type === 'purchase'
                        ? '<span class="badge bg-danger">مشتريات</span>'
                        : '<span class="badge bg-success">دفعة</span>'}
                </td>
                <td>
                    ${transaction.type === 'purchase'
                        ? `<span class="text-danger">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`
                        : `<span class="text-success">${formatKuwaitiCurrency(parseFloat(transaction.amount))}</span>`}
                </td>
                <td>${transaction.description || '-'}</td>
                <td>${userName}</td>
                <td>
                    <button class="btn btn-sm btn-outline-secondary print-receipt-btn" data-transaction-id="${transaction.id}" data-transaction-type="${transaction.type}" data-student-id="${transaction.student_id}">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="7" class="text-center">
                لا توجد معاملات تطابق معايير البحث.
            </td>
        `;
        tableBody.appendChild(row);
    }

    // Update summary
    document.getElementById('total-purchases').textContent = formatKuwaitiCurrency(totalPurchases);
    document.getElementById('total-payments').textContent = formatKuwaitiCurrency(totalPayments);

    const balance = totalPurchases - totalPayments;
    const balanceElement = document.getElementById('balance');

    if (balance > 0) {
        balanceElement.textContent = formatKuwaitiCurrency(balance);
        balanceElement.className = 'text-danger';
    } else if (balance < 0) {
        balanceElement.textContent = formatKuwaitiCurrency(Math.abs(balance));
        balanceElement.className = 'text-success';
    } else {
        balanceElement.textContent = '0 فلس';
        balanceElement.className = '';
    }
}

// Format date function
function formatDate(dateString) {
    const date = new Date(dateString);
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
}

// Setup print functionality
function setupPrintFunctionality() {
    // Add main print button event listener
    document.getElementById('print-btn').addEventListener('click', function() {
        // Get filter values for report title
        const studentId = document.getElementById('student_id').value;
        const type = document.getElementById('type').value;
        const dateFrom = document.getElementById('date_from').value;
        const dateTo = document.getElementById('date_to').value;

        // Get data from localStorage
        const transactions = JSON.parse(localStorage.getItem('transactions')) || [];
        const students = JSON.parse(localStorage.getItem('students')) || [];

        // Create filters object
        const filters = {};

        if (studentId) {
            filters.studentId = studentId;
        }

        if (type) {
            filters.type = type;
        }

        if (dateFrom) {
            filters.dateFrom = dateFrom;
        }

        if (dateTo) {
            filters.dateTo = dateTo;
        }

        // Print transactions report
        printTransactionsTable(transactions, students, filters);
    });

    // Add event listeners for receipt print buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.print-receipt-btn')) {
            const button = e.target.closest('.print-receipt-btn');
            const transactionId = button.getAttribute('data-transaction-id');
            const transactionType = button.getAttribute('data-transaction-type');
            const studentId = button.getAttribute('data-student-id');

            if (!studentId || !transactionId) {
                alert('بيانات غير مكتملة');
                return;
            }

            // Get data from localStorage
            const students = JSON.parse(localStorage.getItem('students')) || [];
            const transactions = JSON.parse(localStorage.getItem('transactions')) || [];

            // Find student and transaction
            const student = students.find(s => s.id == studentId);
            const transaction = transactions.find(t => t.id == transactionId);

            if (!student || !transaction) {
                alert('لم يتم العثور على البيانات');
                return;
            }

            // Print appropriate receipt based on transaction type
            if (transactionType === 'purchase') {
                printPurchaseInvoice(transaction, student);
            } else if (transactionType === 'payment') {
                printPaymentReceipt(transaction, student);
            }
        }
    });
}
