<?php
// Include authentication
require_once '../config/auth.php';
// Require login
requireLogin();
// Include database connection
require_once '../config/db.php';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = $_POST['name'] ?? '';
    $class = $_POST['class'] ?? '';
    $student_id = $_POST['student_id'] ?? '';
    $parent_name = $_POST['parent_name'] ?? '';
    $phone_number = $_POST['phone_number'] ?? '';
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم الطالب مطلوب';
    }
    
    if (empty($class)) {
        $errors[] = 'الصف مطلوب';
    }
    
    if (empty($student_id)) {
        $errors[] = 'رقم هوية الطالب مطلوب';
    } else {
        // Check if student_id already exists
        $stmt = $pdo->prepare('SELECT COUNT(*) FROM students WHERE student_id = ?');
        $stmt->execute([$student_id]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = 'رقم هوية الطالب مستخدم بالفعل';
        }
    }
    
    if (empty($parent_name)) {
        $errors[] = 'اسم ولي الأمر مطلوب';
    }
    
    if (empty($phone_number)) {
        $errors[] = 'رقم الجوال مطلوب';
    }
    
    // If no errors, insert student
    if (empty($errors)) {
        $stmt = $pdo->prepare('INSERT INTO students (name, class, student_id, parent_name, phone_number, created_at) VALUES (?, ?, ?, ?, ?, NOW())');
        $result = $stmt->execute([$name, $class, $student_id, $parent_name, $phone_number]);
        
        if ($result) {
            // Set success message
            $_SESSION['flash_message'] = 'تم إضافة الطالب بنجاح';
            $_SESSION['flash_type'] = 'success';
            
            // Redirect to student list
            header('Location: list.php');
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء إضافة الطالب';
        }
    }
}

// Include header
include '../includes/header.php';
?>

<div class="row">
    <!-- Sidebar -->
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- Main content -->
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>إضافة طالب جديد</h2>
            <a href="list.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
        </div>
        
        <!-- Display errors if any -->
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- Add student form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الطالب <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="class" class="form-label">الصف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="class" name="class" value="<?php echo htmlspecialchars($_POST['class'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="student_id" class="form-label">رقم هوية الطالب <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo htmlspecialchars($_POST['student_id'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="parent_name" class="form-label">اسم ولي الأمر <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="parent_name" name="parent_name" value="<?php echo htmlspecialchars($_POST['parent_name'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label">رقم الجوال <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($_POST['phone_number'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-light me-md-2">إعادة تعيين</button>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
